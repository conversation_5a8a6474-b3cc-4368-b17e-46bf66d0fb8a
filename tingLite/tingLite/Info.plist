<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleAllowMixedLocalizations</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>喜马拉雅极速版</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>uting</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>uting</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weibo</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wb1273699813</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>qq</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>tencent101586639</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wx0e84b03479970203</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>xmubtTrace</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>xmtrace</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.ximalaya.tingLite</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>250321</string>
	<key>Channel</key>
	<string>${APP_CHANNEL}</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>ushengsheng.xjb</string>
		<string>iting</string>
		<string>itingpad</string>
		<string>itms-apps</string>
		<string>bytedance</string>
		<string>wechat</string>
		<string>weixin</string>
		<string>weixinULAPI</string>
		<string>sinaweibohd</string>
		<string>sinaweibo</string>
		<string>sinaweibosso</string>
		<string>weibosdk2.5</string>
		<string>weibosdk</string>
		<string>mqq</string>
		<string>mqqwpa</string>
		<string>mqqapi</string>
		<string>mqqOpensdkSSoLogin</string>
		<string>mqqconnect</string>
		<string>mqqopensdkdataline</string>
		<string>mqqopensdkapiV3</string>
		<string>mqqopensdkgrouptribeshare</string>
		<string>mqqopensdkfriend</string>
		<string>mqqopensdkapi</string>
		<string>mqzone</string>
		<string>mqzonewx</string>
		<string>mqzoneopensdkapiV2</string>
		<string>mqzoneopensdkapi19</string>
		<string>mqzoneopensdkapi</string>
		<string>mqqbrowser</string>
		<string>mqzonev2</string>
		<string>mqzoneshare</string>
		<string>mqzoneopensdk</string>
		<string>mqqopensdkapiV2</string>
		<string>wtloginmqq</string>
		<string>wtloginmqq2</string>
		<string>wtloginqzone</string>
		<string>mttbrowser</string>
		<string>openapp.jdmobile</string>
		<string>openapp.jdmoble</string>
		<string>aqhapximalaya1</string>
		<string>aqhapximalaya2</string>
		<string>uconnectlive</string>
		<string>renrenios</string>
		<string>renrenapi</string>
		<string>renren</string>
		<string>renreniphone</string>
		<string>alipay</string>
		<string>alipayshare</string>
		<string>CarLife</string>
		<string>misfitlink</string>
		<string>tbopen</string>
		<string>taobao</string>
		<string>tmall</string>
		<string>dingtalk</string>
		<string>dingtalk-open</string>
		<string>qiyi-iphone</string>
		<string>tenvideo</string>
		<string>youku</string>
		<string>gifshow</string>
		<string>pinduoduo</string>
		<string>snssdk141</string>
		<string>qqnews</string>
		<string>BaiduSSO</string>
		<string>iosamap</string>
		<string>baidumap</string>
		<string>imeituan</string>
		<string>dianping</string>
		<string>qqmusic</string>
		<string>orpheus</string>
		<string>kugouURL</string>
		<string>com.kuwo.kwmusic.kwmusicForKwsing</string>
		<string>com.sogou.sogouinput</string>
		<string>BaiduIMShop</string>
		<string>ucbrowser</string>
		<string>wb1462309810</string>
		<string>its</string>
		<string>leo</string>
		<string>com.iyaya.mmbang</string>
		<string>palfish-read</string>
		<string>itingKid</string>
		<string>taobaolite</string>
		<string>puapp</string>
		<string>xunlei</string>
		<string>tgclub</string>
		<string>wxf309d9dee32b6815</string>
		<string>baidustudentapp</string>
		<string>ithome</string>
		<string>topmobile</string>
		<string>zenglish9b19bd0263cd</string>
		<string>huazhuapp</string>
		<string>jinxintechnamibox</string>
		<string>growing.bffa2a959d20f382</string>
		<string>zjwhcampus</string>
		<string>blackboard</string>
		<string>wxquestion</string>
		<string>teiron2273</string>
		<string>bsport</string>
		<string>ZTKtest</string>
		<string>med66515292232</string>
		<string>qqshoujiguanjia</string>
		<string>xcfapp</string>
		<string>com.kingsoft.www.office.wpsoffice</string>
		<string>com.fenbi.ape.gwy</string>
		<string>xhsdiscover</string>
		<string>autohome</string>
		<string>zaloan</string>
		<string>vipshop</string>
		<string>huanbei</string>
		<string>carowner</string>
		<string>yquploadlog</string>
		<string>haodfappscheme</string>
		<string>weidianbuyer</string>
		<string>cmblife</string>
		<string>blued</string>
		<string>com.sinyee.babybus.ranch</string>
		<string>jiayuan</string>
		<string>smoba1104466820</string>
		<string>cloudmusicopenapi</string>
		<string>WisdomTreeTeacher</string>
		<string>zhilianzhaopin</string>
		<string>TinmanRead</string>
		<string>bcz</string>
		<string>tantanapp</string>
		<string>ctclient</string>
		<string>baidumusic</string>
		<string>zhaoliangji</string>
		<string>yymobile</string>
		<string>psbcmbank</string>
		<string>jiandanketang</string>
		<string>zhihu</string>
		<string>qcc</string>
		<string>jjlordunion</string>
		<string>mlolapp</string>
		<string>gjj.shanp.com</string>
		<string>dingoavipis9izydgjzryq</string>
		<string>imhotel</string>
		<string>dmkj</string>
		<string>yishifuwu</string>
		<string>wpsoffice</string>
		<string>chinaacc514598865</string>
		<string>bookclub</string>
		<string>com.sinyee.babybus.chef</string>
		<string>cntvCBox</string>
		<string>HUSHILOW</string>
		<string>etao</string>
		<string>duxiaomanloan</string>
		<string>tmallopen</string>
		<string>meetyou.yunqi</string>
		<string>lptd</string>
		<string>ai.zuoye.app</string>
		<string>askmybaby</string>
		<string>qqmail</string>
		<string>xueanquanpublish</string>
		<string>ydcourse</string>
		<string>feishu</string>
		<string>fenqile</string>
		<string>spdbccc</string>
		<string>iciba</string>
		<string>tpybx</string>
		<string>tiebasdk</string>
		<string>fleamarket</string>
		<string>picsart</string>
		<string>kuaikan</string>
		<string>inicoapp</string>
		<string>com.aibang.TransportationCommittee</string>
		<string>hsrjap</string>
		<string>mogujie</string>
		<string>eastmoneyjijin</string>
		<string>bankabc</string>
		<string>bankabc</string>
		<string>duapp</string>
		<string>WisdomTree</string>
		<string>easicare</string>
		<string>ulike</string>
		<string>wx55ca0a6e99054ebe</string>
		<string>zaapp</string>
		<string>jdmobilejdpay</string>
		<string>sfickn</string>
		<string>meituxiuxiu</string>
		<string>eleme</string>
		<string>travelguide</string>
		<string>weishi</string>
		<string>com.szzc.szzc</string>
		<string>wyyktiphone</string>
		<string>douyutv</string>
		<string>itakeawaybiz</string>
		<string>vuevideo</string>
		<string>m.ymzy.cn</string>
		<string>myxj</string>
		<string>dianwodaDWDRider</string>
		<string>tencent101448318.content</string>
		<string>AMIHexin</string>
		<string>suishenxue3</string>
		<string>shenlansikao</string>
		<string>baiduhaokan</string>
		<string>qxb</string>
		<string>baiduyun</string>
		<string>huatian</string>
		<string>ucardriver</string>
		<string>mihome</string>
		<string>mipay</string>
		<string>yibans</string>
		<string>medlive</string>
		<string>com.cgb.creditcar</string>
		<string>nicomama</string>
		<string>alitrip</string>
		<string>quark</string>
		<string>zhangshanghuayi</string>
		<string>BaiHe</string>
		<string>cfm</string>
		<string>elemeCrowdsource</string>
		<string>yingyusiliujitiku.exam8.com</string>
		<string>gpsov</string>
		<string>com.icbc.iphoneclient</string>
		<string>iMeituan</string>
		<string>HappyAnimal3</string>
		<string>kwai</string>
		<string>camcardexchange</string>
		<string>MlifeIphone</string>
		<string>qqmap</string>
		<string>com.exam8.yixuewantiku</string>
		<string>anjukebrokeralisdk</string>
		<string>askhomework</string>
		<string>weibosdk3.3</string>
		<string>BossZP</string>
		<string>ewt</string>
		<string>douyinopensdksm</string>
		<string>videocut</string>
		<string>mn9xou</string>
		<string>hundunyanxishe</string>
		<string>sinaweibosso.422729959</string>
		<string>tiantianptu</string>
		<string>airbnb</string>
		<string>dangdang</string>
		<string>CET4FreeAliPaySDK</string>
		<string>com.lanlanlife.shengqianbao</string>
		<string>proginn</string>
		<string>iOSBabySon</string>
		<string>dragon1967</string>
		<string>QDReader</string>
		<string>kaoyan</string>
		<string>kwzy</string>
		<string>R360BigApp</string>
		<string>pddmerchant</string>
		<string>com.sinyee.babybus.amusement</string>
		<string>tencent101201799</string>
		<string>mobilenotes</string>
		<string>tencent100385258</string>
		<string>haodfappscheme</string>
		<string>wuta</string>
		<string>wacai</string>
		<string>neteasegl</string>
		<string>douyinopensdk</string>
		<string>chuman</string>
		<string>psbcMobileBank</string>
		<string>cloudclass</string>
		<string>ksstory</string>
		<string>citicbankdkkj</string>
		<string>dfcfwpayment</string>
		<string>epocket.xingshulin.com</string>
		<string>TUNIUAPP</string>
		<string>momochat</string>
		<string>meituanqcsr</string>
		<string>babyTing</string>
		<string>didiudriver</string>
		<string>faceu</string>
		<string>kekebaby</string>
		<string>yuwenparent</string>
		<string>tuhu</string>
		<string>mukewang</string>
		<string>cn.12306</string>
		<string>suishengxue8</string>
		<string>capp</string>
		<string>iosdidi</string>
		<string>openapp.jddj.uppay</string>
		<string>guazi</string>
		<string>driver</string>
		<string>bitautoWidget</string>
		<string>mqqsecure</string>
		<string>Haojiazhang</string>
		<string>JianSheBankApp</string>
		<string>wework</string>
		<string>wegameapp</string>
		<string>tencentrm</string>
		<string>mdb</string>
		<string>GkztcWight</string>
		<string>yddict</string>
		<string>soul</string>
		<string>xueqiu</string>
		<string>xtccallwatch</string>
		<string>igetapp</string>
		<string>palifeapp</string>
		<string>snssdk32</string>
		<string>wx95f80c93488910f2</string>
		<string>keep</string>
		<string>kwai.videoshare</string>
		<string>qbb6</string>
		<string>qqbrowser</string>
		<string>docplatform</string>
		<string>hanju</string>
		<string>zcblbjjj</string>
		<string>huoshanopensdk</string>
		<string>SQYCDriver</string>
		<string>weibo</string>
		<string>com.xuexiaoyi.xxy</string>
		<string>mtj1f8a24ba0c</string>
		<string>zhuanzhuan</string>
		<string>wemeet</string>
		<string>didiudriver</string>
		<string>ycteacher</string>
		<string>huoshanalipayshare</string>
		<string>taobaolive</string>
		<string>shimo</string>
		<string>wangxin</string>
		<string>cmbmobilebank</string>
		<string>kwting</string>
		<string>en51cc</string>
		<string>butter</string>
		<string>lagouhr</string>
		<string>lexuegaokao</string>
		<string>CtripWireless</string>
		<string>wxwork</string>
		<string>wemeet</string>
		<string>solar</string>
		<string>meipian</string>
		<string>mtxx.meitu.com</string>
		<string>QuPeiYin</string>
		<string>auto.snssdk.com</string>
		<string>shihuo</string>
		<string>xeswangxiao</string>
		<string>neteaseVopen</string>
		<string>alisupplier</string>
		<string>googlechrome</string>
		<string>EasyJoyShop</string>
		<string>om.exam8.erjiantiku</string>
		<string>tmri12123</string>
		<string>zuiyou</string>
		<string>uppaywallet</string>
		<string>bdlicai</string>
		<string>alitripebk</string>
		<string>sijijunApp</string>
		<string>cn.10086.app</string>
		<string>baoguoxia</string>
		<string>xzielts636</string>
		<string>KYGuideline</string>
		<string>app.soyoung</string>
		<string>neutral</string>
		<string>qichehui</string>
		<string>bilibili</string>
		<string>diyidanApp</string>
		<string>ctrip</string>
		<string>iOSSparkEnglish</string>
		<string>QunariPhone</string>
		<string>jiaoshipai</string>
		<string>baiduboxapp</string>
		<string>baiduboxlite</string>
		<string>wbmain</string>
		<string>mpoauth</string>
		<string>crowdsource</string>
		<string>wx17f7ffa7af6731ed</string>
		<string>pregnancyapp</string>
		<string>nextradio</string>
		<string>xtuonesuperfriday</string>
		<string>wacaicreditcardmanager</string>
		<string>acfun</string>
		<string>qqpim</string>
		<string>abctime</string>
		<string>qiancheng</string>
		<string>wbganji</string>
		<string>wb2473782039</string>
		<string>zhuntiku</string>
		<string>ljshfax</string>
		<string>watermarkcamera</string>
		<string>kingscamp</string>
		<string>zhongguorenbao</string>
		<string>com.sogou.sogouinput.ext</string>
		<string>oauth</string>
		<string>ccbmobilebank</string>
		<string>wwdzsafepay</string>
		<string>doubanradio</string>
		<string>yghsh</string>
		<string>dingdongneighborhood</string>
		<string>safetyeducationplatform</string>
		<string>aliwx9264946004</string>
		<string>meituanwaimai</string>
		<string>ctripOAuth</string>
		<string>buzhigk</string>
		<string>wxc1b82b90807bbd11</string>
		<string>bookingApp</string>
		<string>ayh</string>
		<string>Bestpay</string>
		<string>ztjy.teacher.applink</string>
		<string>linkedin</string>
		<string>meituanqcstaxi</string>
		<string>loan.ppdai.com</string>
		<string>toefl1216c2</string>
		<string>QQmusic</string>
		<string>hupu</string>
		<string>meituanpayment</string>
		<string>me.ele.napos</string>
		<string>KugouKtvUrl</string>
		<string>imgotv</string>
		<string>qmkege</string>
		<string>wx1e6cd1827b854244</string>
		<string>wx19116f0310d09fe5</string>
		<string>muapp</string>
		<string>antiLostWatch</string>
		<string>TKOnline</string>
		<string>cnpcshscheme</string>
		<string>shixiseng</string>
		<string>kaola</string>
		<string>dzhiphone</string>
		<string>wanxiao</string>
		<string>ZMParentAlipay</string>
		<string>My0ffer767577465</string>
		<string>openguangyin</string>
		<string>tctravelApp</string>
		<string>ZeusGeekTime</string>
		<string>ttpod</string>
		<string>suningebuy</string>
		<string>openjx</string>
		<string>openapp.jdpingou</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>用于添加配乐时访问媒体库加入配乐</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>为给您提供蓝牙设备播放音频能力，请允许我们访问您的蓝牙</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>为给您提供蓝牙设备播放音频能力，请允许我们访问您的蓝牙</string>
	<key>NSCameraUsageDescription</key>
	<string>为给您提供拍摄能力(音频版权申诉)，请允许我们访问您的摄像头</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>为给您提供当地音频内容(听电台)，请允许我们访问您的位置信息</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>为给您提供当地音频内容(听电台)，请允许我们访问您的位置信息</string>
	<key>NSLocationUsageDescription</key>
	<string>为给您提供当地音频内容(听电台)，请允许我们访问您的位置信息</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>为给您提供当地音频内容(听电台)，请允许我们访问您的位置信息</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>访问您的麦克风用于录制音频</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>为给您提供图片保存到相册能力(音频版权申诉)，请允许我们访问您的相册</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>为给您提供相册照片或视频发送能力(音频版权申诉)，请允许我们访问您的相册</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>为给您呈献更精彩的节目推荐，请允许我们访问您的设备标识符</string>
	<key>SKAdNetworkItems</key>
	<array>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>238da6jt44.skadnetwork</string>
		</dict>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>x2jnk7ly8j.skadnetwork</string>
		</dict>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>22mmun2rn5.skadnetwork</string>
		</dict>
	</array>
	<key>UIAppFonts</key>
	<array>
		<string>XimaZhiboti-Regular.ttf</string>
	</array>
	<key>UIApplicationShortcutItems</key>
	<array>
		<dict>
			<key>UIApplicationShortcutItemIconFile</key>
			<string>3d_ic_nextplay_black</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>继续上次播放</string>
			<key>UIApplicationShortcutItemType</key>
			<string>continueLastPlay</string>
		</dict>
		<dict>
			<key>UIApplicationShortcutItemIconFile</key>
			<string>3d_ic_recent</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>最近播放</string>
			<key>UIApplicationShortcutItemType</key>
			<string>playHistory</string>
		</dict>
		<dict>
			<key>UIApplicationShortcutItemIconFile</key>
			<string>3d_ic_search</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>搜索节目</string>
			<key>UIApplicationShortcutItemType</key>
			<string>search</string>
		</dict>
		<dict>
			<key>UIApplicationShortcutItemIconFile</key>
			<string>3d_ic_coin</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>赚金币</string>
			<key>UIApplicationShortcutItemType</key>
			<string>earnCoin</string>
		</dict>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<false/>
	<key>UIStatusBarHidden</key>
	<true/>
	<key>UIStatusBarTintParameters</key>
	<dict>
		<key>UINavigationBar</key>
		<dict>
			<key>Style</key>
			<string>UIBarStyleDefault</string>
			<key>Translucent</key>
			<false/>
		</dict>
	</dict>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
</dict>
</plist>
