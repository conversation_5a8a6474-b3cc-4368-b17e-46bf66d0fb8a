//
//  XMIAdABTest.swift
//  XMADXModule
//
//  Created by xiaodong2.zhang on 2025/7/1.
//  Copyright © 2025 ximalaya. All rights reserved.
//

import Foundation

// MARK: - XMIAdABTest Swift Implementation
/// Swift版本的广告ABTest功能，参考OC版本XMIAdABTest实现
@objcMembers
public class XMIAdABTest: NSObject {

    // MARK: - Private Properties

    /// AB测试配置字典，使用单例模式缓存
    private static var _abLabConfigDic: [String: Any]?
    private static let configQueue = DispatchQueue(label: "com.ximalaya.ad.abtest.config", attributes: .concurrent)

    /// 获取AB测试配置字典
    private static var abLabConfigDic: [String: Any]? {
        return configQueue.sync {
            if _abLabConfigDic == nil {
                // 通过 initAdx 初始化后，配置数据会被缓存
                // 这里使用 UserDefaults 或其他方式获取配置数据
                // 由于 XMADXModule 是独立 framework，避免直接依赖 XMIAdConfigData
                _abLabConfigDic = loadConfigFromCache()
            }
            return _abLabConfigDic
        }
    }

    /// 从缓存中加载配置数据
    private static func loadConfigFromCache() -> [String: Any]? {
        // 尝试从 UserDefaults 获取缓存的配置
        if let data = UserDefaults.standard.data(forKey: "XMIAdABTestConfig"),
           let config = try? JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
            return config
        }

        // 如果没有缓存，返回空字典
        return [:]
    }

    // MARK: - Public Methods

    /// 获取Bool类型的AB测试值
    /// - Parameters:
    ///   - key: AB测试的key
    ///   - defaultValue: 默认值
    /// - Returns: Bool值
    public static func getBoolValue(withKey key: String, defaultValue: Bool) -> Bool {
        guard let value = getValue(withKey: key) else {
            return defaultValue
        }

        if let boolValue = value as? Bool {
            return boolValue
        } else if let numberValue = value as? NSNumber {
            return numberValue.boolValue
        } else if let stringValue = value as? String {
            return stringValue.lowercased() == "true" || stringValue == "1"
        }

        return defaultValue
    }

    /// 获取Integer类型的AB测试值
    /// - Parameters:
    ///   - key: AB测试的key
    ///   - defaultValue: 默认值
    /// - Returns: Integer值
    public static func getIntegerValue(withKey key: String, defaultValue: Int) -> Int {
        guard let value = getValue(withKey: key) else {
            return defaultValue
        }

        if let intValue = value as? Int {
            return intValue
        } else if let numberValue = value as? NSNumber {
            return numberValue.intValue
        } else if let stringValue = value as? String {
            return Int(stringValue) ?? defaultValue
        }

        return defaultValue
    }

    /// 获取String类型的AB测试值
    /// - Parameters:
    ///   - key: AB测试的key
    ///   - defaultValue: 默认值
    /// - Returns: String值
    public static func getStrValue(withKey key: String, defaultValue: String) -> String {
        guard let value = getValue(withKey: key) else {
            return defaultValue
        }

        if let stringValue = value as? String {
            return stringValue
        } else {
            return String(describing: value)
        }
    }

    /// 获取Double类型的AB测试值
    /// - Parameters:
    ///   - key: AB测试的key
    ///   - defaultValue: 默认值
    /// - Returns: Double值
    public static func getDoubleValue(withKey key: String, defaultValue: Double) -> Double {
        guard let value = getValue(withKey: key) else {
            return defaultValue
        }

        if let doubleValue = value as? Double {
            return doubleValue
        } else if let numberValue = value as? NSNumber {
            return numberValue.doubleValue
        } else if let stringValue = value as? String {
            return Double(stringValue) ?? defaultValue
        }

        return defaultValue
    }

    /// 获取原始值
    /// - Parameter key: AB测试的key
    /// - Returns: 原始值，可能为nil
    public static func getValue(withKey key: String) -> Any? {
        guard let configDic = abLabConfigDic else {
            return nil
        }

        let result = configDic[key]

        // 过滤NSNull
        if result is NSNull {
            return nil
        }

        return result
    }

    /// 获取JSON对象
    /// - Parameters:
    ///   - key: AB测试的key
    ///   - defaultObject: 默认对象
    /// - Returns: 解析后的JSON对象
    public static func getJsonObject(withKey key: String, defaultObject: Any?) -> Any? {
        let stringValue = getStrValue(withKey: key, defaultValue: "")

        if stringValue.isEmpty {
            return defaultObject
        }

        guard let data = stringValue.data(using: .utf8) else {
            return defaultObject
        }

        do {
            let jsonObject = try JSONSerialization.jsonObject(with: data, options: [])
            return jsonObject
        } catch {
            return defaultObject
        }
    }

    // MARK: - Cache Management

    /// 清除配置缓存（用于配置更新时）
    public static func clearConfigCache() {
        configQueue.async(flags: .barrier) {
            _abLabConfigDic = nil
            // 同时清除 UserDefaults 缓存
            UserDefaults.standard.removeObject(forKey: "XMIAdABTestConfig")
        }
    }

    /// 手动更新配置缓存
    /// - Parameter config: 新的配置字典
    public static func updateConfigCache(_ config: [String: Any]?) {
        configQueue.async(flags: .barrier) {
            _abLabConfigDic = config

            // 同时更新 UserDefaults 缓存
            if let config = config {
                if let data = try? JSONSerialization.data(withJSONObject: config, options: []) {
                    UserDefaults.standard.set(data, forKey: "XMIAdABTestConfig")
                }
            } else {
                UserDefaults.standard.removeObject(forKey: "XMIAdABTestConfig")
            }
        }
    }

    /// 从 OC 端更新配置（供 initAdx 调用）
    /// - Parameter configData: 配置数据的 JSON 字符串
    @objc public static func updateConfigFromInitAdx(_ configData: String?) {
        guard let configData = configData,
              let data = configData.data(using: .utf8),
              let config = try? JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] else {
            return
        }

        updateConfigCache(config)
    }
}

// MARK: - Objective-C Compatibility Extensions
extension XMIAdABTest {

    /// OC兼容方法：获取Bool值
    @objc(getBoolValueWithKey:defaultValue:)
    public static func objc_getBoolValue(withKey key: String, defaultValue: Bool) -> Bool {
        return getBoolValue(withKey: key, defaultValue: defaultValue)
    }

    /// OC兼容方法：获取Integer值
    @objc(getIntegerValueWithKey:defaultValue:)
    public static func objc_getIntegerValue(withKey key: String, defaultValue: Int) -> Int {
        return getIntegerValue(withKey: key, defaultValue: defaultValue)
    }

    /// OC兼容方法：获取String值
    @objc(getStrValueWithKey:defaultValue:)
    public static func objc_getStrValue(withKey key: String, defaultValue: String) -> String {
        return getStrValue(withKey: key, defaultValue: defaultValue)
    }

    /// OC兼容方法：获取Double值
    @objc(getDoubleValueWithKey:defaultValue:)
    public static func objc_getDoubleValue(withKey key: String, defaultValue: Double) -> Double {
        return getDoubleValue(withKey: key, defaultValue: defaultValue)
    }

    /// OC兼容方法：获取原始值
    @objc(getValueWithKey:)
    public static func objc_getValue(withKey key: String) -> Any? {
        return getValue(withKey: key)
    }

    /// OC兼容方法：获取JSON对象
    @objc(getJsonObjectWithKey:defaultObject:)
    public static func objc_getJsonObject(withKey key: String, defaultObject: Any?) -> Any? {
        return getJsonObject(withKey: key, defaultObject: defaultObject)
    }
}
