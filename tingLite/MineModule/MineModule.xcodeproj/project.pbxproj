// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		04F1645827900A2E00017C2C /* XMMineBannerCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 04F1645727900A2E00017C2C /* XMMineBannerCell.swift */; };
		0B4DB5A6232389D600F459C8 /* ShareModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0B4DB5A5232389D600F459C8 /* ShareModule.framework */; };
		0B620A8D231D4EC800AC556F /* XMMineUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B620A8C231D4EC800AC556F /* XMMineUtils.swift */; };
		22142BDF26E0AAB900FBCA98 /* XMHistoryModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 22142BDE26E0AAB900FBCA98 /* XMHistoryModule.framework */; };
		222EB98126D8E19500F5FDD7 /* XMLFavoriteAudioViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 222EB98026D8E19300F5FDD7 /* XMLFavoriteAudioViewController.swift */; };
		222EB98326DCB81B00F5FDD7 /* XMLModeSwitchAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 222EB98226DCB81B00F5FDD7 /* XMLModeSwitchAlertView.swift */; };
		22434DCD27BB840A00B78FBF /* XMLFavoritePlayletTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22434DCC27BB840A00B78FBF /* XMLFavoritePlayletTableViewCell.swift */; };
		225C8FBB26DE1F570077C851 /* XMLFavoriteAudioTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225C8FBA26DE1F570077C851 /* XMLFavoriteAudioTableViewCell.swift */; };
		225C8FBD26DE37500077C851 /* XMLFavoriteViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225C8FBC26DE37500077C851 /* XMLFavoriteViewModel.swift */; };
		225E24282727D77C0087EF4F /* XMLFavoriteManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 225E24272727D77C0087EF4F /* XMLFavoriteManager.swift */; };
		2263013027A2966500081101 /* XMMyFavoriteViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2263012F27A2966500081101 /* XMMyFavoriteViewController.swift */; };
		2263013427A29C1B00081101 /* XMLFavoritePlayletViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2263013327A29C1B00081101 /* XMLFavoritePlayletViewController.swift */; };
		66CC66E122136A70996F22C7 /* Pods_MineModuleTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 68F990146C08ADE478C056C2 /* Pods_MineModuleTests.framework */; };
		8817FBEE22D873DA00DC0084 /* MineModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8817FBE422D873DA00DC0084 /* MineModule.framework */; };
		8817FBF322D873DA00DC0084 /* MineModuleTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8817FBF222D873DA00DC0084 /* MineModuleTests.swift */; };
		8817FBF522D873DA00DC0084 /* MineModule.h in Headers */ = {isa = PBXBuildFile; fileRef = 8817FBE722D873DA00DC0084 /* MineModule.h */; settings = {ATTRIBUTES = (Public, ); }; };
		8868CF1822F42F21009A5F61 /* Media.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8868CF1622F42F07009A5F61 /* Media.xcassets */; };
		8868CF1922F42F2D009A5F61 /* MineMedia.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 8868CF1022F42EF2009A5F61 /* MineMedia.bundle */; };
		8887851522E96C90001A7782 /* XMImageLoader.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8887851322E96C83001A7782 /* XMImageLoader.swift */; };
		95004DE2278ED42A0091E9DE /* XMLClimbABTestViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95004DE1278ED42A0091E9DE /* XMLClimbABTestViewController.swift */; };
		9567593A255E7CF600689C17 /* xmlMine.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 95675939255E7CF500689C17 /* xmlMine.ttf */; };
		95833BA826C63B3A007E5D6A /* XMLMineViewADCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95833BA726C63B3A007E5D6A /* XMLMineViewADCell.swift */; };
		9589F4EE27980FA200160519 /* XMLClimbABItemViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9589F4ED27980F9F00160519 /* XMLClimbABItemViewController.swift */; };
		9596F7F426141FB0002D96F5 /* XMAlertModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9596F7F326141FB0002D96F5 /* XMAlertModule.framework */; };
		95A64D942788267000401A77 /* XMLClimbFootballViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95A64D932788267000401A77 /* XMLClimbFootballViewController.swift */; };
		95A64D9627882FB500401A77 /* XMLClimbFootballSubWidgets.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95A64D9527882FB500401A77 /* XMLClimbFootballSubWidgets.swift */; };
		95A7D7FC275772B2008FA889 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 95A7D7FB275772B2008FA889 /* <EMAIL> */; };
		95B5FC5C25636AE200E8C98D /* profile_img_userhead_wp.webp in Resources */ = {isa = PBXBuildFile; fileRef = 95B5FC5B25636AE200E8C98D /* profile_img_userhead_wp.webp */; };
		95D400A2248F24E70093F596 /* CommBusiness.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 95D400A1248F24E70093F596 /* CommBusiness.framework */; };
		95F051E324ED026E0051E7DD /* XMADXModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 95F051E224ED026E0051E7DD /* XMADXModule.framework */; };
		B0579BE822FE6E1D0023BC65 /* XMNetworkModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B0579BE722FE6E1D0023BC65 /* XMNetworkModule.framework */; };
		B06E0D0522FAA9D800F4B93D /* RouterModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B06E0D0422FAA9D800F4B93D /* RouterModule.framework */; };
		B080E5A9230BCF6F00058643 /* MediaModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B080E5A8230BCF6F00058643 /* MediaModule.framework */; };
		B0B6037722F992E2004CE92D /* BaseModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B0B6037622F992E2004CE92D /* BaseModule.framework */; };
		B6259195256949E200CC01C9 /* XMWebModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B6259194256949E200CC01C9 /* XMWebModule.framework */; };
		B6356F6E27578A65007C9241 /* XMChildrenPwdViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6356F6D27578A65007C9241 /* XMChildrenPwdViewController.swift */; };
		B635E4AF275774D6006E6AC2 /* topbg_Youth <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B635E4AE275774D6006E6AC2 /* topbg_Youth <EMAIL> */; };
		B6640E5027574E9300407178 /* XMOtherSettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6640E4B27574E9300407178 /* XMOtherSettingViewController.swift */; };
		B6640E5127574E9300407178 /* XMRecentDeviceAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6640E4C27574E9300407178 /* XMRecentDeviceAlertView.swift */; };
		B6640E5227574E9300407178 /* XMSystemPermissionsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6640E4D27574E9300407178 /* XMSystemPermissionsCell.swift */; };
		B6640E5327574E9300407178 /* XMSystemPermissionsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6640E4E27574E9300407178 /* XMSystemPermissionsViewController.swift */; };
		B6640E5427574E9300407178 /* XMAccessPermissionsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6640E4F27574E9300407178 /* XMAccessPermissionsViewController.swift */; };
		B6640E57275756D000407178 /* XMChildrenModeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6640E56275756D000407178 /* XMChildrenModeViewController.swift */; };
		B66C316B252073DF00A4876C /* XMICFontMine.swift in Sources */ = {isa = PBXBuildFile; fileRef = B66C316A252073DF00A4876C /* XMICFontMine.swift */; };
		B672978C26CBA540003BF6C7 /* XMLUserPortrayalViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B672978626CBA540003BF6C7 /* XMLUserPortrayalViewModel.swift */; };
		B672978D26CBA540003BF6C7 /* XMLUserPortrayalViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B672978726CBA540003BF6C7 /* XMLUserPortrayalViewController.swift */; };
		B672978E26CBA540003BF6C7 /* XMLUserPortrayalCardItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = B672978926CBA540003BF6C7 /* XMLUserPortrayalCardItem.swift */; };
		B672979026CBA540003BF6C7 /* XMLUserPortrayalStepTwoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B672978B26CBA540003BF6C7 /* XMLUserPortrayalStepTwoView.swift */; };
		B672979326CBA567003BF6C7 /* XMLPreferenceCardItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = B672979226CBA567003BF6C7 /* XMLPreferenceCardItem.swift */; };
		B67297A426CBD27A003BF6C7 /* XMKayouPreferenceView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B67297A126CBD27A003BF6C7 /* XMKayouPreferenceView.swift */; };
		B67297A526CBD27A003BF6C7 /* XMKayouPreferenceViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B67297A226CBD27A003BF6C7 /* XMKayouPreferenceViewController.swift */; };
		B67297A626CBD27A003BF6C7 /* XMKayouCardItemCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = B67297A326CBD27A003BF6C7 /* XMKayouCardItemCell.swift */; };
		B687C152269D2F8A0071B6B1 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B687C151269D2F8A0071B6B1 /* <EMAIL> */; };
		B69B47502779C1990059D5BA /* XMCommentModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B69B474F2779C1990059D5BA /* XMCommentModule.framework */; };
		B6B5F29D26B27E1200775BE5 /* FeedbackManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F28E26B27E1200775BE5 /* FeedbackManager.swift */; };
		B6B5F29E26B27E1200775BE5 /* XMLMineViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F29026B27E1200775BE5 /* XMLMineViewModel.swift */; };
		B6B5F29F26B27E1200775BE5 /* XMLMineViewInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F29126B27E1200775BE5 /* XMLMineViewInfoModel.swift */; };
		B6B5F2A026B27E1200775BE5 /* XMLMineViewOperationCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F29326B27E1200775BE5 /* XMLMineViewOperationCell.swift */; };
		B6B5F2A126B27E1200775BE5 /* XMLMineViewHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F29426B27E1200775BE5 /* XMLMineViewHeaderView.swift */; };
		B6B5F2A426B27E1200775BE5 /* XMLMineViewVipBannerCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F29726B27E1200775BE5 /* XMLMineViewVipBannerCell.swift */; };
		B6B5F2A526B27E1200775BE5 /* XMLSettingCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F29826B27E1200775BE5 /* XMLSettingCell.swift */; };
		B6B5F2A626B27E1200775BE5 /* XMLMineBaseCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F29926B27E1200775BE5 /* XMLMineBaseCell.swift */; };
		B6B5F2A726B27E1200775BE5 /* XMLMineSettingCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F29A26B27E1200775BE5 /* XMLMineSettingCell.swift */; };
		B6B5F2A826B27E1200775BE5 /* XMToolTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F29B26B27E1200775BE5 /* XMToolTableViewCell.swift */; };
		B6B5F2A926B27E1200775BE5 /* XMLMineViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F29C26B27E1200775BE5 /* XMLMineViewController.swift */; };
		B6B5F2C326B27E1E00775BE5 /* XMSettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2AC26B27E1E00775BE5 /* XMSettingViewController.swift */; };
		B6B5F2C426B27E1E00775BE5 /* XMSettingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2AD26B27E1E00775BE5 /* XMSettingViewModel.swift */; };
		B6B5F2C526B27E1E00775BE5 /* XMCacheCleanViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2AE26B27E1E00775BE5 /* XMCacheCleanViewController.swift */; };
		B6B5F2C626B27E1E00775BE5 /* XMLDeviceIdTableViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2AF26B27E1E00775BE5 /* XMLDeviceIdTableViewController.swift */; };
		B6B5F2C726B27E1E00775BE5 /* XMDiagnoseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2B126B27E1E00775BE5 /* XMDiagnoseViewController.swift */; };
		B6B5F2C826B27E1E00775BE5 /* XMUserDiagnoseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2B226B27E1E00775BE5 /* XMUserDiagnoseViewController.swift */; };
		B6B5F2C926B27E1E00775BE5 /* XMNetDiagnoseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2B326B27E1E00775BE5 /* XMNetDiagnoseViewController.swift */; };
		B6B5F2CA26B27E1E00775BE5 /* XMDiagnoseDataSquare.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2B426B27E1E00775BE5 /* XMDiagnoseDataSquare.swift */; };
		B6B5F2CC26B27E1E00775BE5 /* XMLClimbViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2B626B27E1E00775BE5 /* XMLClimbViewController.swift */; };
		B6B5F2CD26B27E1E00775BE5 /* XMQualitySettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2B726B27E1E00775BE5 /* XMQualitySettingViewController.swift */; };
		B6B5F2CE26B27E1E00775BE5 /* XMLClimbDataSquare.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2B826B27E1E00775BE5 /* XMLClimbDataSquare.swift */; };
		B6B5F2CF26B27E1E00775BE5 /* XMLVClimbViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2B926B27E1E00775BE5 /* XMLVClimbViewController.swift */; };
		B6B5F2D026B27E1E00775BE5 /* XMPieceLayoutButtonItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2BB26B27E1E00775BE5 /* XMPieceLayoutButtonItem.swift */; };
		B6B5F2D126B27E1E00775BE5 /* XMPieceLayoutData.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2BC26B27E1E00775BE5 /* XMPieceLayoutData.swift */; };
		B6B5F2D226B27E1E00775BE5 /* XMPieceLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2BD26B27E1E00775BE5 /* XMPieceLayout.swift */; };
		B6B5F2D326B27E1F00775BE5 /* XMPieceLayoutView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2BE26B27E1E00775BE5 /* XMPieceLayoutView.swift */; };
		B6B5F2D426B27E1F00775BE5 /* XMPieceLayoutInputItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2BF26B27E1E00775BE5 /* XMPieceLayoutInputItem.swift */; };
		B6B5F2D526B27E1F00775BE5 /* XMPieceLayoutTitleHeader.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2C026B27E1E00775BE5 /* XMPieceLayoutTitleHeader.swift */; };
		B6B5F2D626B27E1F00775BE5 /* XMCacheCleanCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2C126B27E1E00775BE5 /* XMCacheCleanCell.swift */; };
		B6B5F2E526B280E700775BE5 /* XMMineModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6B5F2E426B280E700775BE5 /* XMMineModel.swift */; };
		B6C0AA41258B44E800C24877 /* ModuleRouterIMP.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6C0AA40258B44E800C24877 /* ModuleRouterIMP.swift */; };
		B6DD543324E50F8000B32F81 /* MineModuleEntry.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6DD543224E50F8000B32F81 /* MineModuleEntry.swift */; };
		B6E1771E2759C08F000FF694 /* XMLTeenageAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6E1771D2759C08F000FF694 /* XMLTeenageAlertView.swift */; };
		B6EFB5C425345B3C009DE98A /* XMUtilModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B6EFB5C325345B3C009DE98A /* XMUtilModule.framework */; };
		B6EFB5C825345B52009DE98A /* XMConfigModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B6EFB5C725345B51009DE98A /* XMConfigModule.framework */; };
		B6F7001C26DF86970031E4AD /* interestCardV9.json in Resources */ = {isa = PBXBuildFile; fileRef = B6F7001726DF86970031E4AD /* interestCardV9.json */; };
		B6F7001D26DF86970031E4AD /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B6F7001826DF86970031E4AD /* <EMAIL> */; };
		B6F7001E26DF86970031E4AD /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B6F7001926DF86970031E4AD /* <EMAIL> */; };
		B6F7001F26DF86970031E4AD /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B6F7001A26DF86970031E4AD /* <EMAIL> */; };
		B6F7002026DF86970031E4AD /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B6F7001B26DF86970031E4AD /* <EMAIL> */; };
		B6F7002426DF96450031E4AD /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B6F7002326DF96450031E4AD /* <EMAIL> */; };
		B6F999D525345FEE00B5CC60 /* LoginModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B6EFB5BF25345AE5009DE98A /* LoginModule.framework */; };
		B6FDCDAC2684705200B184D3 /* XMLPlazaViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95C96D1F255CE43100299220 /* XMLPlazaViewController.swift */; };
		FE57E4F544A8CDC56FC1875A /* Pods_MineModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EDB4148A7ED1684AD71946FB /* Pods_MineModule.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		8817FBEF22D873DA00DC0084 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8817FBDB22D873DA00DC0084 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8817FBE322D873DA00DC0084;
			remoteInfo = MineModule;
		};
		8868CF1A22F42F32009A5F61 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8817FBDB22D873DA00DC0084 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8868CF0F22F42EF2009A5F61;
			remoteInfo = MineMedia;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0474BF3D2729647F0021FB9D /* XMReaderModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMReaderModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		04F1645727900A2E00017C2C /* XMMineBannerCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMMineBannerCell.swift; sourceTree = "<group>"; };
		0B4DB5A5232389D600F459C8 /* ShareModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = ShareModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		0B620A8C231D4EC800AC556F /* XMMineUtils.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMMineUtils.swift; sourceTree = "<group>"; };
		22142BDE26E0AAB900FBCA98 /* XMHistoryModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMHistoryModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		222EB98026D8E19300F5FDD7 /* XMLFavoriteAudioViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLFavoriteAudioViewController.swift; sourceTree = "<group>"; };
		222EB98226DCB81B00F5FDD7 /* XMLModeSwitchAlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLModeSwitchAlertView.swift; sourceTree = "<group>"; };
		22434DCC27BB840A00B78FBF /* XMLFavoritePlayletTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLFavoritePlayletTableViewCell.swift; sourceTree = "<group>"; };
		225C8FBA26DE1F570077C851 /* XMLFavoriteAudioTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLFavoriteAudioTableViewCell.swift; sourceTree = "<group>"; };
		225C8FBC26DE37500077C851 /* XMLFavoriteViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLFavoriteViewModel.swift; sourceTree = "<group>"; };
		225E24272727D77C0087EF4F /* XMLFavoriteManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLFavoriteManager.swift; sourceTree = "<group>"; };
		2263012F27A2966500081101 /* XMMyFavoriteViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMMyFavoriteViewController.swift; sourceTree = "<group>"; };
		2263013327A29C1B00081101 /* XMLFavoritePlayletViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLFavoritePlayletViewController.swift; sourceTree = "<group>"; };
		2380B7842A15AF61065828EB /* Pods-MineModuleTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MineModuleTests.release.xcconfig"; path = "Target Support Files/Pods-MineModuleTests/Pods-MineModuleTests.release.xcconfig"; sourceTree = "<group>"; };
		3FDB838607B693354792BA24 /* Pods-MineModule.alpha.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MineModule.alpha.xcconfig"; path = "Target Support Files/Pods-MineModule/Pods-MineModule.alpha.xcconfig"; sourceTree = "<group>"; };
		6869B130BF881A58B1C88E9A /* Pods-MineModuleTests.alpha.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MineModuleTests.alpha.xcconfig"; path = "Target Support Files/Pods-MineModuleTests/Pods-MineModuleTests.alpha.xcconfig"; sourceTree = "<group>"; };
		68F990146C08ADE478C056C2 /* Pods_MineModuleTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_MineModuleTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		8817FBE422D873DA00DC0084 /* MineModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = MineModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		8817FBE722D873DA00DC0084 /* MineModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MineModule.h; sourceTree = "<group>"; };
		8817FBE822D873DA00DC0084 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8817FBED22D873DA00DC0084 /* MineModuleTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MineModuleTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		8817FBF222D873DA00DC0084 /* MineModuleTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MineModuleTests.swift; sourceTree = "<group>"; };
		8817FBF422D873DA00DC0084 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8817FC4422DB1FF400DC0084 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		8868CF1022F42EF2009A5F61 /* MineMedia.bundle */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MineMedia.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		8868CF1222F42EF2009A5F61 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8868CF1622F42F07009A5F61 /* Media.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Media.xcassets; sourceTree = "<group>"; };
		8887851322E96C83001A7782 /* XMImageLoader.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMImageLoader.swift; sourceTree = "<group>"; };
		95004DE1278ED42A0091E9DE /* XMLClimbABTestViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLClimbABTestViewController.swift; sourceTree = "<group>"; };
		953A30D42480E9C7002FFA66 /* XMOCBridge.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMOCBridge.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		953A30D62480EE24002FFA66 /* RNKitModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = RNKitModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		95675939255E7CF500689C17 /* xmlMine.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = xmlMine.ttf; sourceTree = "<group>"; };
		95833BA726C63B3A007E5D6A /* XMLMineViewADCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLMineViewADCell.swift; sourceTree = "<group>"; };
		958512D5273543E7004F674A /* XMReaderModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMReaderModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9589F4ED27980F9F00160519 /* XMLClimbABItemViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLClimbABItemViewController.swift; sourceTree = "<group>"; };
		9596F7F326141FB0002D96F5 /* XMAlertModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMAlertModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		95A64D932788267000401A77 /* XMLClimbFootballViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLClimbFootballViewController.swift; sourceTree = "<group>"; };
		95A64D9527882FB500401A77 /* XMLClimbFootballSubWidgets.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLClimbFootballSubWidgets.swift; sourceTree = "<group>"; };
		95A7D7FB275772B2008FA889 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		95B5FC5B25636AE200E8C98D /* profile_img_userhead_wp.webp */ = {isa = PBXFileReference; lastKnownFileType = file; path = profile_img_userhead_wp.webp; sourceTree = "<group>"; };
		95C96D1F255CE43100299220 /* XMLPlazaViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMLPlazaViewController.swift; sourceTree = "<group>"; };
		95D400A1248F24E70093F596 /* CommBusiness.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = CommBusiness.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		95F051E224ED026E0051E7DD /* XMADXModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMADXModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		AF7D2C8FC55A7767B616FB0F /* Pods-MineModule.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MineModule.release.xcconfig"; path = "Target Support Files/Pods-MineModule/Pods-MineModule.release.xcconfig"; sourceTree = "<group>"; };
		B0579BE722FE6E1D0023BC65 /* XMNetworkModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMNetworkModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B06E0D0422FAA9D800F4B93D /* RouterModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = RouterModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B06E0D0C22FB0A3600F4B93D /* AudioModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = AudioModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B080E5A8230BCF6F00058643 /* MediaModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = MediaModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0B6037622F992E2004CE92D /* BaseModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = BaseModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B6259194256949E200CC01C9 /* XMWebModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMWebModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B6356F6D27578A65007C9241 /* XMChildrenPwdViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMChildrenPwdViewController.swift; sourceTree = "<group>"; };
		B635E4AE275774D6006E6AC2 /* topbg_Youth <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "topbg_Youth <EMAIL>"; sourceTree = "<group>"; };
		B6640E4B27574E9300407178 /* XMOtherSettingViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMOtherSettingViewController.swift; sourceTree = "<group>"; };
		B6640E4C27574E9300407178 /* XMRecentDeviceAlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMRecentDeviceAlertView.swift; sourceTree = "<group>"; };
		B6640E4D27574E9300407178 /* XMSystemPermissionsCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMSystemPermissionsCell.swift; sourceTree = "<group>"; };
		B6640E4E27574E9300407178 /* XMSystemPermissionsViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMSystemPermissionsViewController.swift; sourceTree = "<group>"; };
		B6640E4F27574E9300407178 /* XMAccessPermissionsViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMAccessPermissionsViewController.swift; sourceTree = "<group>"; };
		B6640E56275756D000407178 /* XMChildrenModeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMChildrenModeViewController.swift; sourceTree = "<group>"; };
		B66C316A252073DF00A4876C /* XMICFontMine.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMICFontMine.swift; sourceTree = "<group>"; };
		B672978626CBA540003BF6C7 /* XMLUserPortrayalViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLUserPortrayalViewModel.swift; sourceTree = "<group>"; };
		B672978726CBA540003BF6C7 /* XMLUserPortrayalViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLUserPortrayalViewController.swift; sourceTree = "<group>"; };
		B672978926CBA540003BF6C7 /* XMLUserPortrayalCardItem.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLUserPortrayalCardItem.swift; sourceTree = "<group>"; };
		B672978B26CBA540003BF6C7 /* XMLUserPortrayalStepTwoView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLUserPortrayalStepTwoView.swift; sourceTree = "<group>"; };
		B672979226CBA567003BF6C7 /* XMLPreferenceCardItem.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLPreferenceCardItem.swift; sourceTree = "<group>"; };
		B67297A126CBD27A003BF6C7 /* XMKayouPreferenceView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMKayouPreferenceView.swift; sourceTree = "<group>"; };
		B67297A226CBD27A003BF6C7 /* XMKayouPreferenceViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMKayouPreferenceViewController.swift; sourceTree = "<group>"; };
		B67297A326CBD27A003BF6C7 /* XMKayouCardItemCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMKayouCardItemCell.swift; sourceTree = "<group>"; };
		B687C151269D2F8A0071B6B1 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B69B474F2779C1990059D5BA /* XMCommentModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMCommentModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B6B5F28E26B27E1200775BE5 /* FeedbackManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FeedbackManager.swift; sourceTree = "<group>"; };
		B6B5F29026B27E1200775BE5 /* XMLMineViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLMineViewModel.swift; sourceTree = "<group>"; };
		B6B5F29126B27E1200775BE5 /* XMLMineViewInfoModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLMineViewInfoModel.swift; sourceTree = "<group>"; };
		B6B5F29326B27E1200775BE5 /* XMLMineViewOperationCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLMineViewOperationCell.swift; sourceTree = "<group>"; };
		B6B5F29426B27E1200775BE5 /* XMLMineViewHeaderView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLMineViewHeaderView.swift; sourceTree = "<group>"; };
		B6B5F29526B27E1200775BE5 /* XMLInfoGameCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLInfoGameCell.swift; sourceTree = "<group>"; };
		B6B5F29726B27E1200775BE5 /* XMLMineViewVipBannerCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLMineViewVipBannerCell.swift; sourceTree = "<group>"; };
		B6B5F29826B27E1200775BE5 /* XMLSettingCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLSettingCell.swift; sourceTree = "<group>"; };
		B6B5F29926B27E1200775BE5 /* XMLMineBaseCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLMineBaseCell.swift; sourceTree = "<group>"; };
		B6B5F29A26B27E1200775BE5 /* XMLMineSettingCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLMineSettingCell.swift; sourceTree = "<group>"; };
		B6B5F29B26B27E1200775BE5 /* XMToolTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMToolTableViewCell.swift; sourceTree = "<group>"; };
		B6B5F29C26B27E1200775BE5 /* XMLMineViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLMineViewController.swift; sourceTree = "<group>"; };
		B6B5F2AC26B27E1E00775BE5 /* XMSettingViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMSettingViewController.swift; sourceTree = "<group>"; };
		B6B5F2AD26B27E1E00775BE5 /* XMSettingViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMSettingViewModel.swift; sourceTree = "<group>"; };
		B6B5F2AE26B27E1E00775BE5 /* XMCacheCleanViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMCacheCleanViewController.swift; sourceTree = "<group>"; };
		B6B5F2AF26B27E1E00775BE5 /* XMLDeviceIdTableViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLDeviceIdTableViewController.swift; sourceTree = "<group>"; };
		B6B5F2B126B27E1E00775BE5 /* XMDiagnoseViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMDiagnoseViewController.swift; sourceTree = "<group>"; };
		B6B5F2B226B27E1E00775BE5 /* XMUserDiagnoseViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMUserDiagnoseViewController.swift; sourceTree = "<group>"; };
		B6B5F2B326B27E1E00775BE5 /* XMNetDiagnoseViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMNetDiagnoseViewController.swift; sourceTree = "<group>"; };
		B6B5F2B426B27E1E00775BE5 /* XMDiagnoseDataSquare.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMDiagnoseDataSquare.swift; sourceTree = "<group>"; };
		B6B5F2B626B27E1E00775BE5 /* XMLClimbViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLClimbViewController.swift; sourceTree = "<group>"; };
		B6B5F2B726B27E1E00775BE5 /* XMQualitySettingViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMQualitySettingViewController.swift; sourceTree = "<group>"; };
		B6B5F2B826B27E1E00775BE5 /* XMLClimbDataSquare.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLClimbDataSquare.swift; sourceTree = "<group>"; };
		B6B5F2B926B27E1E00775BE5 /* XMLVClimbViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLVClimbViewController.swift; sourceTree = "<group>"; };
		B6B5F2BB26B27E1E00775BE5 /* XMPieceLayoutButtonItem.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMPieceLayoutButtonItem.swift; sourceTree = "<group>"; };
		B6B5F2BC26B27E1E00775BE5 /* XMPieceLayoutData.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMPieceLayoutData.swift; sourceTree = "<group>"; };
		B6B5F2BD26B27E1E00775BE5 /* XMPieceLayout.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMPieceLayout.swift; sourceTree = "<group>"; };
		B6B5F2BE26B27E1E00775BE5 /* XMPieceLayoutView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMPieceLayoutView.swift; sourceTree = "<group>"; };
		B6B5F2BF26B27E1E00775BE5 /* XMPieceLayoutInputItem.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMPieceLayoutInputItem.swift; sourceTree = "<group>"; };
		B6B5F2C026B27E1E00775BE5 /* XMPieceLayoutTitleHeader.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMPieceLayoutTitleHeader.swift; sourceTree = "<group>"; };
		B6B5F2C126B27E1E00775BE5 /* XMCacheCleanCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMCacheCleanCell.swift; sourceTree = "<group>"; };
		B6B5F2E426B280E700775BE5 /* XMMineModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMMineModel.swift; sourceTree = "<group>"; };
		B6C0AA40258B44E800C24877 /* ModuleRouterIMP.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ModuleRouterIMP.swift; sourceTree = "<group>"; };
		B6DD543224E50F8000B32F81 /* MineModuleEntry.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MineModuleEntry.swift; sourceTree = "<group>"; };
		B6E1771D2759C08F000FF694 /* XMLTeenageAlertView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = XMLTeenageAlertView.swift; sourceTree = "<group>"; };
		B6EFB5BF25345AE5009DE98A /* LoginModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = LoginModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B6EFB5C325345B3C009DE98A /* XMUtilModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMUtilModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B6EFB5C725345B51009DE98A /* XMConfigModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMConfigModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B6F7001726DF86970031E4AD /* interestCardV9.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = interestCardV9.json; sourceTree = "<group>"; };
		B6F7001826DF86970031E4AD /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		B6F7001926DF86970031E4AD /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B6F7001A26DF86970031E4AD /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B6F7001B26DF86970031E4AD /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = file; path = "<EMAIL>"; sourceTree = "<group>"; };
		B6F7002326DF96450031E4AD /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		C8916068062908E0EE61EE95 /* Pods-MineModule.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MineModule.debug.xcconfig"; path = "Target Support Files/Pods-MineModule/Pods-MineModule.debug.xcconfig"; sourceTree = "<group>"; };
		D509C61874903D12A43C3E68 /* Pods-MineModuleTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MineModuleTests.debug.xcconfig"; path = "Target Support Files/Pods-MineModuleTests/Pods-MineModuleTests.debug.xcconfig"; sourceTree = "<group>"; };
		EDB4148A7ED1684AD71946FB /* Pods_MineModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_MineModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		8817FBE122D873DA00DC0084 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B6EFB5C425345B3C009DE98A /* XMUtilModule.framework in Frameworks */,
				0B4DB5A6232389D600F459C8 /* ShareModule.framework in Frameworks */,
				B0579BE822FE6E1D0023BC65 /* XMNetworkModule.framework in Frameworks */,
				95F051E324ED026E0051E7DD /* XMADXModule.framework in Frameworks */,
				B080E5A9230BCF6F00058643 /* MediaModule.framework in Frameworks */,
				B06E0D0522FAA9D800F4B93D /* RouterModule.framework in Frameworks */,
				B0B6037722F992E2004CE92D /* BaseModule.framework in Frameworks */,
				95D400A2248F24E70093F596 /* CommBusiness.framework in Frameworks */,
				B6F999D525345FEE00B5CC60 /* LoginModule.framework in Frameworks */,
				9596F7F426141FB0002D96F5 /* XMAlertModule.framework in Frameworks */,
				22142BDF26E0AAB900FBCA98 /* XMHistoryModule.framework in Frameworks */,
				B6EFB5C825345B52009DE98A /* XMConfigModule.framework in Frameworks */,
				B69B47502779C1990059D5BA /* XMCommentModule.framework in Frameworks */,
				FE57E4F544A8CDC56FC1875A /* Pods_MineModule.framework in Frameworks */,
				B6259195256949E200CC01C9 /* XMWebModule.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8817FBEA22D873DA00DC0084 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8817FBEE22D873DA00DC0084 /* MineModule.framework in Frameworks */,
				66CC66E122136A70996F22C7 /* Pods_MineModuleTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8868CF0D22F42EF2009A5F61 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		222EB97F26D8E14900F5FDD7 /* Favorite */ = {
			isa = PBXGroup;
			children = (
				2263012F27A2966500081101 /* XMMyFavoriteViewController.swift */,
				225C8FBC26DE37500077C851 /* XMLFavoriteViewModel.swift */,
				2263013227A2989F00081101 /* FavoriteAudio */,
				2263013127A2989F00081101 /* FavoritePlaylet */,
			);
			path = Favorite;
			sourceTree = "<group>";
		};
		2263013127A2989F00081101 /* FavoritePlaylet */ = {
			isa = PBXGroup;
			children = (
				2263013327A29C1B00081101 /* XMLFavoritePlayletViewController.swift */,
				22434DCC27BB840A00B78FBF /* XMLFavoritePlayletTableViewCell.swift */,
			);
			path = FavoritePlaylet;
			sourceTree = "<group>";
		};
		2263013227A2989F00081101 /* FavoriteAudio */ = {
			isa = PBXGroup;
			children = (
				225E24272727D77C0087EF4F /* XMLFavoriteManager.swift */,
				222EB98026D8E19300F5FDD7 /* XMLFavoriteAudioViewController.swift */,
				225C8FBA26DE1F570077C851 /* XMLFavoriteAudioTableViewCell.swift */,
			);
			path = FavoriteAudio;
			sourceTree = "<group>";
		};
		674469CEC4B431EB1576B635 /* Pods */ = {
			isa = PBXGroup;
			children = (
				C8916068062908E0EE61EE95 /* Pods-MineModule.debug.xcconfig */,
				AF7D2C8FC55A7767B616FB0F /* Pods-MineModule.release.xcconfig */,
				D509C61874903D12A43C3E68 /* Pods-MineModuleTests.debug.xcconfig */,
				2380B7842A15AF61065828EB /* Pods-MineModuleTests.release.xcconfig */,
				3FDB838607B693354792BA24 /* Pods-MineModule.alpha.xcconfig */,
				6869B130BF881A58B1C88E9A /* Pods-MineModuleTests.alpha.xcconfig */,
			);
			name = Pods;
			path = ../Pods;
			sourceTree = "<group>";
		};
		8817FBDA22D873DA00DC0084 = {
			isa = PBXGroup;
			children = (
				8817FBE622D873DA00DC0084 /* MineModule */,
				8817FBF122D873DA00DC0084 /* MineModuleTests */,
				8868CF1122F42EF2009A5F61 /* MineMedia */,
				8817FBE522D873DA00DC0084 /* Products */,
				674469CEC4B431EB1576B635 /* Pods */,
				ED9303F1765A9618411CA6C6 /* Frameworks */,
				95833BA626C632C3007E5D6A /* Recovered References */,
			);
			sourceTree = "<group>";
		};
		8817FBE522D873DA00DC0084 /* Products */ = {
			isa = PBXGroup;
			children = (
				8817FBE422D873DA00DC0084 /* MineModule.framework */,
				8817FBED22D873DA00DC0084 /* MineModuleTests.xctest */,
				8868CF1022F42EF2009A5F61 /* MineMedia.bundle */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8817FBE622D873DA00DC0084 /* MineModule */ = {
			isa = PBXGroup;
			children = (
				8817FBE722D873DA00DC0084 /* MineModule.h */,
				B6DD543224E50F8000B32F81 /* MineModuleEntry.swift */,
				B66C316A252073DF00A4876C /* XMICFontMine.swift */,
				B6C0AA40258B44E800C24877 /* ModuleRouterIMP.swift */,
				B6B5F28C26B27E1200775BE5 /* Mine */,
				222EB97F26D8E14900F5FDD7 /* Favorite */,
				B6B5F2AA26B27E1E00775BE5 /* Setting */,
				95C96D1E255CE34D00299220 /* Plaza */,
				8887851222E96C77001A7782 /* Utils */,
				8868CF1622F42F07009A5F61 /* Media.xcassets */,
				8817FBE822D873DA00DC0084 /* Info.plist */,
			);
			path = MineModule;
			sourceTree = "<group>";
		};
		8817FBF122D873DA00DC0084 /* MineModuleTests */ = {
			isa = PBXGroup;
			children = (
				8817FBF222D873DA00DC0084 /* MineModuleTests.swift */,
				8817FBF422D873DA00DC0084 /* Info.plist */,
			);
			path = MineModuleTests;
			sourceTree = "<group>";
		};
		8868CF1122F42EF2009A5F61 /* MineMedia */ = {
			isa = PBXGroup;
			children = (
				B635E4AE275774D6006E6AC2 /* topbg_Youth <EMAIL> */,
				B6F7001626DF86970031E4AD /* preference */,
				B687C151269D2F8A0071B6B1 /* <EMAIL> */,
				95B5FC5B25636AE200E8C98D /* profile_img_userhead_wp.webp */,
				95A7D7FB275772B2008FA889 /* <EMAIL> */,
				95675939255E7CF500689C17 /* xmlMine.ttf */,
				8868CF1222F42EF2009A5F61 /* Info.plist */,
			);
			path = MineMedia;
			sourceTree = "<group>";
		};
		8887851222E96C77001A7782 /* Utils */ = {
			isa = PBXGroup;
			children = (
				8887851322E96C83001A7782 /* XMImageLoader.swift */,
				0B620A8C231D4EC800AC556F /* XMMineUtils.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		95833BA626C632C3007E5D6A /* Recovered References */ = {
			isa = PBXGroup;
			children = (
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
		95A64D922788263C00401A77 /* Climb */ = {
			isa = PBXGroup;
			children = (
				B6B5F2B626B27E1E00775BE5 /* XMLClimbViewController.swift */,
				B6B5F2B826B27E1E00775BE5 /* XMLClimbDataSquare.swift */,
				B6B5F2B926B27E1E00775BE5 /* XMLVClimbViewController.swift */,
				95A64D932788267000401A77 /* XMLClimbFootballViewController.swift */,
				95004DE1278ED42A0091E9DE /* XMLClimbABTestViewController.swift */,
				9589F4ED27980F9F00160519 /* XMLClimbABItemViewController.swift */,
				95A64D9527882FB500401A77 /* XMLClimbFootballSubWidgets.swift */,
			);
			path = Climb;
			sourceTree = "<group>";
		};
		95C96D1E255CE34D00299220 /* Plaza */ = {
			isa = PBXGroup;
			children = (
				95C96D1F255CE43100299220 /* XMLPlazaViewController.swift */,
			);
			path = Plaza;
			sourceTree = "<group>";
		};
		B6640E4A27574E9300407178 /* OtherSetting */ = {
			isa = PBXGroup;
			children = (
				B6640E55275756A200407178 /* ChildrenMode */,
				B6640E4B27574E9300407178 /* XMOtherSettingViewController.swift */,
				B6640E4C27574E9300407178 /* XMRecentDeviceAlertView.swift */,
				B6640E4D27574E9300407178 /* XMSystemPermissionsCell.swift */,
				B6640E4E27574E9300407178 /* XMSystemPermissionsViewController.swift */,
				B6640E4F27574E9300407178 /* XMAccessPermissionsViewController.swift */,
			);
			path = OtherSetting;
			sourceTree = "<group>";
		};
		B6640E55275756A200407178 /* ChildrenMode */ = {
			isa = PBXGroup;
			children = (
				B6E1771D2759C08F000FF694 /* XMLTeenageAlertView.swift */,
				B6356F6D27578A65007C9241 /* XMChildrenPwdViewController.swift */,
				B6640E56275756D000407178 /* XMChildrenModeViewController.swift */,
			);
			path = ChildrenMode;
			sourceTree = "<group>";
		};
		B672978426CBA540003BF6C7 /* Normal */ = {
			isa = PBXGroup;
			children = (
				B672979126CBA567003BF6C7 /* Model */,
				B672978526CBA540003BF6C7 /* ViewModel */,
				B672978726CBA540003BF6C7 /* XMLUserPortrayalViewController.swift */,
				B672978826CBA540003BF6C7 /* View */,
			);
			path = Normal;
			sourceTree = "<group>";
		};
		B672978526CBA540003BF6C7 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
				B672978626CBA540003BF6C7 /* XMLUserPortrayalViewModel.swift */,
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		B672978826CBA540003BF6C7 /* View */ = {
			isa = PBXGroup;
			children = (
				B672978926CBA540003BF6C7 /* XMLUserPortrayalCardItem.swift */,
				B672978B26CBA540003BF6C7 /* XMLUserPortrayalStepTwoView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		B672979126CBA567003BF6C7 /* Model */ = {
			isa = PBXGroup;
			children = (
				B672979226CBA567003BF6C7 /* XMLPreferenceCardItem.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		B672979E26CBD27A003BF6C7 /* Kayou */ = {
			isa = PBXGroup;
			children = (
				B672979F26CBD27A003BF6C7 /* ViewModel */,
				B67297A026CBD27A003BF6C7 /* View */,
			);
			path = Kayou;
			sourceTree = "<group>";
		};
		B672979F26CBD27A003BF6C7 /* ViewModel */ = {
			isa = PBXGroup;
			children = (
			);
			path = ViewModel;
			sourceTree = "<group>";
		};
		B67297A026CBD27A003BF6C7 /* View */ = {
			isa = PBXGroup;
			children = (
				B67297A126CBD27A003BF6C7 /* XMKayouPreferenceView.swift */,
				B67297A226CBD27A003BF6C7 /* XMKayouPreferenceViewController.swift */,
				B67297A326CBD27A003BF6C7 /* XMKayouCardItemCell.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		B6B5F28C26B27E1200775BE5 /* Mine */ = {
			isa = PBXGroup;
			children = (
				B6B5F28D26B27E1200775BE5 /* Feedback */,
				B6B5F28F26B27E1200775BE5 /* Model */,
				B6B5F29226B27E1200775BE5 /* View */,
				B6B5F29C26B27E1200775BE5 /* XMLMineViewController.swift */,
			);
			path = Mine;
			sourceTree = "<group>";
		};
		B6B5F28D26B27E1200775BE5 /* Feedback */ = {
			isa = PBXGroup;
			children = (
				B6B5F28E26B27E1200775BE5 /* FeedbackManager.swift */,
			);
			path = Feedback;
			sourceTree = "<group>";
		};
		B6B5F28F26B27E1200775BE5 /* Model */ = {
			isa = PBXGroup;
			children = (
				B6B5F2E426B280E700775BE5 /* XMMineModel.swift */,
				B6B5F29026B27E1200775BE5 /* XMLMineViewModel.swift */,
				B6B5F29126B27E1200775BE5 /* XMLMineViewInfoModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		B6B5F29226B27E1200775BE5 /* View */ = {
			isa = PBXGroup;
			children = (
				95833BA726C63B3A007E5D6A /* XMLMineViewADCell.swift */,
				B6B5F29326B27E1200775BE5 /* XMLMineViewOperationCell.swift */,
				B6B5F29426B27E1200775BE5 /* XMLMineViewHeaderView.swift */,
				B6B5F29526B27E1200775BE5 /* XMLInfoGameCell.swift */,
				B6B5F29726B27E1200775BE5 /* XMLMineViewVipBannerCell.swift */,
				B6B5F29826B27E1200775BE5 /* XMLSettingCell.swift */,
				B6B5F29926B27E1200775BE5 /* XMLMineBaseCell.swift */,
				B6B5F29A26B27E1200775BE5 /* XMLMineSettingCell.swift */,
				B6B5F29B26B27E1200775BE5 /* XMToolTableViewCell.swift */,
				222EB98226DCB81B00F5FDD7 /* XMLModeSwitchAlertView.swift */,
				04F1645727900A2E00017C2C /* XMMineBannerCell.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		B6B5F2AA26B27E1E00775BE5 /* Setting */ = {
			isa = PBXGroup;
			children = (
				95A64D922788263C00401A77 /* Climb */,
				B6B5F2B026B27E1E00775BE5 /* Diagnose */,
				B6B5F2D726B27E3900775BE5 /* UserPortrayal */,
				B6B5F2BA26B27E1E00775BE5 /* Widgets */,
				B6640E4A27574E9300407178 /* OtherSetting */,
				B6B5F2AC26B27E1E00775BE5 /* XMSettingViewController.swift */,
				B6B5F2AD26B27E1E00775BE5 /* XMSettingViewModel.swift */,
				B6B5F2AE26B27E1E00775BE5 /* XMCacheCleanViewController.swift */,
				B6B5F2AF26B27E1E00775BE5 /* XMLDeviceIdTableViewController.swift */,
				B6B5F2B726B27E1E00775BE5 /* XMQualitySettingViewController.swift */,
				B6B5F2C126B27E1E00775BE5 /* XMCacheCleanCell.swift */,
			);
			path = Setting;
			sourceTree = "<group>";
		};
		B6B5F2B026B27E1E00775BE5 /* Diagnose */ = {
			isa = PBXGroup;
			children = (
				B6B5F2B126B27E1E00775BE5 /* XMDiagnoseViewController.swift */,
				B6B5F2B226B27E1E00775BE5 /* XMUserDiagnoseViewController.swift */,
				B6B5F2B326B27E1E00775BE5 /* XMNetDiagnoseViewController.swift */,
				B6B5F2B426B27E1E00775BE5 /* XMDiagnoseDataSquare.swift */,
			);
			path = Diagnose;
			sourceTree = "<group>";
		};
		B6B5F2BA26B27E1E00775BE5 /* Widgets */ = {
			isa = PBXGroup;
			children = (
				B6B5F2BB26B27E1E00775BE5 /* XMPieceLayoutButtonItem.swift */,
				B6B5F2BC26B27E1E00775BE5 /* XMPieceLayoutData.swift */,
				B6B5F2BD26B27E1E00775BE5 /* XMPieceLayout.swift */,
				B6B5F2BE26B27E1E00775BE5 /* XMPieceLayoutView.swift */,
				B6B5F2BF26B27E1E00775BE5 /* XMPieceLayoutInputItem.swift */,
				B6B5F2C026B27E1E00775BE5 /* XMPieceLayoutTitleHeader.swift */,
			);
			path = Widgets;
			sourceTree = "<group>";
		};
		B6B5F2D726B27E3900775BE5 /* UserPortrayal */ = {
			isa = PBXGroup;
			children = (
				B672979E26CBD27A003BF6C7 /* Kayou */,
				B672978426CBA540003BF6C7 /* Normal */,
			);
			path = UserPortrayal;
			sourceTree = "<group>";
		};
		B6F7001626DF86970031E4AD /* preference */ = {
			isa = PBXGroup;
			children = (
				B6F7002326DF96450031E4AD /* <EMAIL> */,
				B6F7001726DF86970031E4AD /* interestCardV9.json */,
				B6F7001826DF86970031E4AD /* <EMAIL> */,
				B6F7001926DF86970031E4AD /* <EMAIL> */,
				B6F7001A26DF86970031E4AD /* <EMAIL> */,
				B6F7001B26DF86970031E4AD /* <EMAIL> */,
			);
			path = preference;
			sourceTree = "<group>";
		};
		ED9303F1765A9618411CA6C6 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				B69B474F2779C1990059D5BA /* XMCommentModule.framework */,
				958512D5273543E7004F674A /* XMReaderModule.framework */,
				0474BF3D2729647F0021FB9D /* XMReaderModule.framework */,
				22142BDE26E0AAB900FBCA98 /* XMHistoryModule.framework */,
				9596F7F326141FB0002D96F5 /* XMAlertModule.framework */,
				B6259194256949E200CC01C9 /* XMWebModule.framework */,
				B6EFB5C725345B51009DE98A /* XMConfigModule.framework */,
				B6EFB5C325345B3C009DE98A /* XMUtilModule.framework */,
				B6EFB5BF25345AE5009DE98A /* LoginModule.framework */,
				95F051E224ED026E0051E7DD /* XMADXModule.framework */,
				95D400A1248F24E70093F596 /* CommBusiness.framework */,
				953A30D62480EE24002FFA66 /* RNKitModule.framework */,
				953A30D42480E9C7002FFA66 /* XMOCBridge.framework */,
				0B4DB5A5232389D600F459C8 /* ShareModule.framework */,
				B080E5A8230BCF6F00058643 /* MediaModule.framework */,
				B0579BE722FE6E1D0023BC65 /* XMNetworkModule.framework */,
				B06E0D0C22FB0A3600F4B93D /* AudioModule.framework */,
				B06E0D0422FAA9D800F4B93D /* RouterModule.framework */,
				B0B6037622F992E2004CE92D /* BaseModule.framework */,
				8817FC4422DB1FF400DC0084 /* SwiftUI.framework */,
				EDB4148A7ED1684AD71946FB /* Pods_MineModule.framework */,
				68F990146C08ADE478C056C2 /* Pods_MineModuleTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		8817FBDF22D873DA00DC0084 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8817FBF522D873DA00DC0084 /* MineModule.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		8817FBE322D873DA00DC0084 /* MineModule */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8817FBF822D873DA00DC0084 /* Build configuration list for PBXNativeTarget "MineModule" */;
			buildPhases = (
				2494A8606BB43EDB47AE21E2 /* [CP] Check Pods Manifest.lock */,
				8817FBDF22D873DA00DC0084 /* Headers */,
				8817FBE022D873DA00DC0084 /* Sources */,
				8817FBE122D873DA00DC0084 /* Frameworks */,
				8817FBE222D873DA00DC0084 /* Resources */,
				69EF467DED9D3C210F18503D /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8868CF1B22F42F32009A5F61 /* PBXTargetDependency */,
			);
			name = MineModule;
			productName = MineModule;
			productReference = 8817FBE422D873DA00DC0084 /* MineModule.framework */;
			productType = "com.apple.product-type.framework";
		};
		8817FBEC22D873DA00DC0084 /* MineModuleTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8817FBFB22D873DA00DC0084 /* Build configuration list for PBXNativeTarget "MineModuleTests" */;
			buildPhases = (
				C57A9467328FD71C7A199050 /* [CP] Check Pods Manifest.lock */,
				8817FBE922D873DA00DC0084 /* Sources */,
				8817FBEA22D873DA00DC0084 /* Frameworks */,
				8817FBEB22D873DA00DC0084 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8817FBF022D873DA00DC0084 /* PBXTargetDependency */,
			);
			name = MineModuleTests;
			productName = MineModuleTests;
			productReference = 8817FBED22D873DA00DC0084 /* MineModuleTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		8868CF0F22F42EF2009A5F61 /* MineMedia */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8868CF1322F42EF2009A5F61 /* Build configuration list for PBXNativeTarget "MineMedia" */;
			buildPhases = (
				8868CF0C22F42EF2009A5F61 /* Sources */,
				8868CF0D22F42EF2009A5F61 /* Frameworks */,
				8868CF0E22F42EF2009A5F61 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MineMedia;
			productName = MineMedia;
			productReference = 8868CF1022F42EF2009A5F61 /* MineMedia.bundle */;
			productType = "com.apple.product-type.bundle";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8817FBDB22D873DA00DC0084 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1100;
				LastUpgradeCheck = 1100;
				ORGANIZATIONNAME = ximalaya;
				TargetAttributes = {
					8817FBE322D873DA00DC0084 = {
						CreatedOnToolsVersion = 11.0;
						LastSwiftMigration = 1100;
					};
					8817FBEC22D873DA00DC0084 = {
						CreatedOnToolsVersion = 11.0;
					};
					8868CF0F22F42EF2009A5F61 = {
						CreatedOnToolsVersion = 11.0;
					};
				};
			};
			buildConfigurationList = 8817FBDE22D873DA00DC0084 /* Build configuration list for PBXProject "MineModule" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 8817FBDA22D873DA00DC0084;
			productRefGroup = 8817FBE522D873DA00DC0084 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8817FBE322D873DA00DC0084 /* MineModule */,
				8817FBEC22D873DA00DC0084 /* MineModuleTests */,
				8868CF0F22F42EF2009A5F61 /* MineMedia */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8817FBE222D873DA00DC0084 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8868CF1922F42F2D009A5F61 /* MineMedia.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8817FBEB22D873DA00DC0084 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8868CF0E22F42EF2009A5F61 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8868CF1822F42F21009A5F61 /* Media.xcassets in Resources */,
				B6F7002026DF86970031E4AD /* <EMAIL> in Resources */,
				95B5FC5C25636AE200E8C98D /* profile_img_userhead_wp.webp in Resources */,
				B6F7001C26DF86970031E4AD /* interestCardV9.json in Resources */,
				B6F7001F26DF86970031E4AD /* <EMAIL> in Resources */,
				B6F7001E26DF86970031E4AD /* <EMAIL> in Resources */,
				B6F7002426DF96450031E4AD /* <EMAIL> in Resources */,
				B687C152269D2F8A0071B6B1 /* <EMAIL> in Resources */,
				95A7D7FC275772B2008FA889 /* <EMAIL> in Resources */,
				9567593A255E7CF600689C17 /* xmlMine.ttf in Resources */,
				B6F7001D26DF86970031E4AD /* <EMAIL> in Resources */,
				B635E4AF275774D6006E6AC2 /* topbg_Youth <EMAIL> in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		2494A8606BB43EDB47AE21E2 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-MineModule-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		69EF467DED9D3C210F18503D /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MineModule/Pods-MineModule-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MineModule/Pods-MineModule-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-MineModule/Pods-MineModule-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C57A9467328FD71C7A199050 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-MineModuleTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8817FBE022D873DA00DC0084 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				222EB98126D8E19500F5FDD7 /* XMLFavoriteAudioViewController.swift in Sources */,
				B6640E57275756D000407178 /* XMChildrenModeViewController.swift in Sources */,
				B67297A426CBD27A003BF6C7 /* XMKayouPreferenceView.swift in Sources */,
				B67297A626CBD27A003BF6C7 /* XMKayouCardItemCell.swift in Sources */,
				B6640E5127574E9300407178 /* XMRecentDeviceAlertView.swift in Sources */,
				04F1645827900A2E00017C2C /* XMMineBannerCell.swift in Sources */,
				B6640E5427574E9300407178 /* XMAccessPermissionsViewController.swift in Sources */,
				9589F4EE27980FA200160519 /* XMLClimbABItemViewController.swift in Sources */,
				B6DD543324E50F8000B32F81 /* MineModuleEntry.swift in Sources */,
				95A64D9627882FB500401A77 /* XMLClimbFootballSubWidgets.swift in Sources */,
				B6640E5327574E9300407178 /* XMSystemPermissionsViewController.swift in Sources */,
				B6B5F2C426B27E1E00775BE5 /* XMSettingViewModel.swift in Sources */,
				B6B5F2CF26B27E1E00775BE5 /* XMLVClimbViewController.swift in Sources */,
				B6B5F29F26B27E1200775BE5 /* XMLMineViewInfoModel.swift in Sources */,
				95833BA826C63B3A007E5D6A /* XMLMineViewADCell.swift in Sources */,
				225C8FBB26DE1F570077C851 /* XMLFavoriteAudioTableViewCell.swift in Sources */,
				B6E1771E2759C08F000FF694 /* XMLTeenageAlertView.swift in Sources */,
				B6B5F2D626B27E1F00775BE5 /* XMCacheCleanCell.swift in Sources */,
				B672978D26CBA540003BF6C7 /* XMLUserPortrayalViewController.swift in Sources */,
				B6B5F2C826B27E1E00775BE5 /* XMUserDiagnoseViewController.swift in Sources */,
				B6B5F29E26B27E1200775BE5 /* XMLMineViewModel.swift in Sources */,
				B6B5F2E526B280E700775BE5 /* XMMineModel.swift in Sources */,
				B6B5F2A026B27E1200775BE5 /* XMLMineViewOperationCell.swift in Sources */,
				B6B5F2A826B27E1200775BE5 /* XMToolTableViewCell.swift in Sources */,
				B6B5F2A526B27E1200775BE5 /* XMLSettingCell.swift in Sources */,
				95004DE2278ED42A0091E9DE /* XMLClimbABTestViewController.swift in Sources */,
				B6B5F29D26B27E1200775BE5 /* FeedbackManager.swift in Sources */,
				2263013027A2966500081101 /* XMMyFavoriteViewController.swift in Sources */,
				222EB98326DCB81B00F5FDD7 /* XMLModeSwitchAlertView.swift in Sources */,
				B6B5F2CD26B27E1E00775BE5 /* XMQualitySettingViewController.swift in Sources */,
				B672979026CBA540003BF6C7 /* XMLUserPortrayalStepTwoView.swift in Sources */,
				B6B5F2A726B27E1200775BE5 /* XMLMineSettingCell.swift in Sources */,
				B6B5F2D226B27E1E00775BE5 /* XMPieceLayout.swift in Sources */,
				0B620A8D231D4EC800AC556F /* XMMineUtils.swift in Sources */,
				B6C0AA41258B44E800C24877 /* ModuleRouterIMP.swift in Sources */,
				B6B5F2CA26B27E1E00775BE5 /* XMDiagnoseDataSquare.swift in Sources */,
				B672979326CBA567003BF6C7 /* XMLPreferenceCardItem.swift in Sources */,
				B6B5F2A426B27E1200775BE5 /* XMLMineViewVipBannerCell.swift in Sources */,
				B6B5F2C626B27E1E00775BE5 /* XMLDeviceIdTableViewController.swift in Sources */,
				B6B5F2D126B27E1E00775BE5 /* XMPieceLayoutData.swift in Sources */,
				B6B5F2C726B27E1E00775BE5 /* XMDiagnoseViewController.swift in Sources */,
				22434DCD27BB840A00B78FBF /* XMLFavoritePlayletTableViewCell.swift in Sources */,
				B67297A526CBD27A003BF6C7 /* XMKayouPreferenceViewController.swift in Sources */,
				B6B5F2A126B27E1200775BE5 /* XMLMineViewHeaderView.swift in Sources */,
				8887851522E96C90001A7782 /* XMImageLoader.swift in Sources */,
				B672978C26CBA540003BF6C7 /* XMLUserPortrayalViewModel.swift in Sources */,
				B672978E26CBA540003BF6C7 /* XMLUserPortrayalCardItem.swift in Sources */,
				B6B5F2CC26B27E1E00775BE5 /* XMLClimbViewController.swift in Sources */,
				B6B5F2D526B27E1F00775BE5 /* XMPieceLayoutTitleHeader.swift in Sources */,
				B6B5F2D326B27E1F00775BE5 /* XMPieceLayoutView.swift in Sources */,
				B66C316B252073DF00A4876C /* XMICFontMine.swift in Sources */,
				B6B5F2C526B27E1E00775BE5 /* XMCacheCleanViewController.swift in Sources */,
				B6B5F2D426B27E1F00775BE5 /* XMPieceLayoutInputItem.swift in Sources */,
				B6356F6E27578A65007C9241 /* XMChildrenPwdViewController.swift in Sources */,
				B6B5F2C326B27E1E00775BE5 /* XMSettingViewController.swift in Sources */,
				B6640E5227574E9300407178 /* XMSystemPermissionsCell.swift in Sources */,
				B6B5F2A926B27E1200775BE5 /* XMLMineViewController.swift in Sources */,
				225E24282727D77C0087EF4F /* XMLFavoriteManager.swift in Sources */,
				B6B5F2CE26B27E1E00775BE5 /* XMLClimbDataSquare.swift in Sources */,
				225C8FBD26DE37500077C851 /* XMLFavoriteViewModel.swift in Sources */,
				B6B5F2C926B27E1E00775BE5 /* XMNetDiagnoseViewController.swift in Sources */,
				2263013427A29C1B00081101 /* XMLFavoritePlayletViewController.swift in Sources */,
				B6B5F2A626B27E1200775BE5 /* XMLMineBaseCell.swift in Sources */,
				B6640E5027574E9300407178 /* XMOtherSettingViewController.swift in Sources */,
				95A64D942788267000401A77 /* XMLClimbFootballViewController.swift in Sources */,
				B6B5F2D026B27E1E00775BE5 /* XMPieceLayoutButtonItem.swift in Sources */,
				B6FDCDAC2684705200B184D3 /* XMLPlazaViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8817FBE922D873DA00DC0084 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8817FBF322D873DA00DC0084 /* MineModuleTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8868CF0C22F42EF2009A5F61 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		8817FBF022D873DA00DC0084 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8817FBE322D873DA00DC0084 /* MineModule */;
			targetProxy = 8817FBEF22D873DA00DC0084 /* PBXContainerItemProxy */;
		};
		8868CF1B22F42F32009A5F61 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8868CF0F22F42EF2009A5F61 /* MineMedia */;
			targetProxy = 8868CF1A22F42F32009A5F61 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		8817FBF622D873DA00DC0084 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		8817FBF722D873DA00DC0084 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "Apple Distribution";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		8817FBF922D873DA00DC0084 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C8916068062908E0EE61EE95 /* Pods-MineModule.debug.xcconfig */;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = NO;
				CLANG_ENABLE_MODULES = YES;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SRCROOT)/../Products\"",
				);
				HEADER_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = MineModule/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"WeChatSDK\"",
					"-l\"WeiboSDK\"",
					"-l\"c++\"",
					"-l\"iconv\"",
					"-l\"resolv\"",
					"-l\"resolv.9\"",
					"-l\"sqlite3\"",
					"-l\"xml2\"",
					"-l\"z\"",
					"-framework",
					"\"AFNetworking\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"AVKit\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"AdSupport\"",
					"-framework",
					"\"BUAdSDK\"",
					"-framework",
					"\"BUFoundation\"",
					"-framework",
					"\"BaiduMobAdSDK\"",
					"-framework",
					"\"CMGameSDK\"",
					"-framework",
					"\"CollectionKit\"",
					"-framework",
					"\"CoreData\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreMedia\"",
					"-framework",
					"\"CoreMotion\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"CryptoSwift\"",
					"-framework",
					"\"FDFullscreenPopGesture\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GDTMobSDK\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"KSAdSDK\"",
					"-framework",
					"\"KeychainAccess\"",
					"-framework",
					"\"Kingfisher\"",
					"-framework",
					"\"KingfisherSwiftUI\"",
					"-framework",
					"\"Lottie\"",
					"-framework",
					"\"MJRefresh\"",
					"-framework",
					"\"MapKit\"",
					"-framework",
					"\"MediaPlayer\"",
					"-framework",
					"\"MessageUI\"",
					"-framework",
					"\"MobileCoreServices\"",
					"-framework",
					"\"PhoneNetSDK\"",
					"-framework",
					"\"PromiseKit\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"QuickLook\"",
					"-framework",
					"\"RxBlocking\"",
					"-framework",
					"\"RxCocoa\"",
					"-framework",
					"\"RxRelay\"",
					"-framework",
					"\"RxSwift\"",
					"-framework",
					"\"SafariServices\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SnapKit\"",
					"-framework",
					"\"StoreKit\"",
					"-framework",
					"\"SwiftyJSON\"",
					"-framework",
					"\"SwiftyUserDefaults\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"TencentOpenAPI\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"WebP\"",
					"-framework",
					"\"XMAccount\"",
					"-framework",
					"\"XMBase\"",
					"-framework",
					"\"XMCategories\"",
					"-framework",
					"\"XMKingfisherWebP\"",
					"-framework",
					"\"XMNetworkRequest\"",
					"-framework",
					"\"XMPKHUD\"",
					"-framework",
					"\"XMThirdParty\"",
					"-framework",
					"\"XMUIKit\"",
					"-framework",
					"\"XMWebImage\"",
					"-framework",
					"\"YYCache\"",
					"-framework",
					"\"iCarousel\"",
					"-framework",
					"\"XMReaderModule\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.MineModule;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SUPPORTS_MACCATALYST = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		8817FBFA22D873DA00DC0084 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AF7D2C8FC55A7767B616FB0F /* Pods-MineModule.release.xcconfig */;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = NO;
				CLANG_ENABLE_MODULES = YES;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SRCROOT)/../Products\"",
				);
				HEADER_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = MineModule/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"WeChatSDK\"",
					"-l\"WeiboSDK\"",
					"-l\"c++\"",
					"-l\"iconv\"",
					"-l\"resolv\"",
					"-l\"resolv.9\"",
					"-l\"sqlite3\"",
					"-l\"xml2\"",
					"-l\"z\"",
					"-framework",
					"\"AFNetworking\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"AVKit\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"AdSupport\"",
					"-framework",
					"\"BUAdSDK\"",
					"-framework",
					"\"BUFoundation\"",
					"-framework",
					"\"BaiduMobAdSDK\"",
					"-framework",
					"\"CMGameSDK\"",
					"-framework",
					"\"CollectionKit\"",
					"-framework",
					"\"CoreData\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreMedia\"",
					"-framework",
					"\"CoreMotion\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"CryptoSwift\"",
					"-framework",
					"\"FDFullscreenPopGesture\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GDTMobSDK\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"KSAdSDK\"",
					"-framework",
					"\"KeychainAccess\"",
					"-framework",
					"\"Kingfisher\"",
					"-framework",
					"\"KingfisherSwiftUI\"",
					"-framework",
					"\"Lottie\"",
					"-framework",
					"\"MJRefresh\"",
					"-framework",
					"\"MapKit\"",
					"-framework",
					"\"MediaPlayer\"",
					"-framework",
					"\"MessageUI\"",
					"-framework",
					"\"MobileCoreServices\"",
					"-framework",
					"\"PhoneNetSDK\"",
					"-framework",
					"\"PromiseKit\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"QuickLook\"",
					"-framework",
					"\"RxBlocking\"",
					"-framework",
					"\"RxCocoa\"",
					"-framework",
					"\"RxRelay\"",
					"-framework",
					"\"RxSwift\"",
					"-framework",
					"\"SafariServices\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SnapKit\"",
					"-framework",
					"\"StoreKit\"",
					"-framework",
					"\"SwiftyJSON\"",
					"-framework",
					"\"SwiftyUserDefaults\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"TencentOpenAPI\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"WebP\"",
					"-framework",
					"\"XMAccount\"",
					"-framework",
					"\"XMBase\"",
					"-framework",
					"\"XMCategories\"",
					"-framework",
					"\"XMKingfisherWebP\"",
					"-framework",
					"\"XMNetworkRequest\"",
					"-framework",
					"\"XMPKHUD\"",
					"-framework",
					"\"XMThirdParty\"",
					"-framework",
					"\"XMUIKit\"",
					"-framework",
					"\"XMWebImage\"",
					"-framework",
					"\"YYCache\"",
					"-framework",
					"\"iCarousel\"",
					"-framework",
					"\"XMReaderModule\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.MineModule;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SUPPORTS_MACCATALYST = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		8817FBFC22D873DA00DC0084 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D509C61874903D12A43C3E68 /* Pods-MineModuleTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				INFOPLIST_FILE = MineModuleTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = mh_dylib;
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.MineModuleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		8817FBFD22D873DA00DC0084 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2380B7842A15AF61065828EB /* Pods-MineModuleTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				INFOPLIST_FILE = MineModuleTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = mh_dylib;
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.MineModuleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		8868CF1422F42EF2009A5F61 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				COMBINE_HIDPI_IMAGES = YES;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = MineMedia/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.MineMedia;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		8868CF1522F42EF2009A5F61 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				COMBINE_HIDPI_IMAGES = YES;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = MineMedia/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.MineMedia;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		B0B722D82351B3D900B23D74 /* Alpha */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "Apple Distribution";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"ALPHA=1",
					"DEBUG=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "ALPHA DEBUG";
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Alpha;
		};
		B0B722D92351B3D900B23D74 /* Alpha */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3FDB838607B693354792BA24 /* Pods-MineModule.alpha.xcconfig */;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = NO;
				CLANG_ENABLE_MODULES = YES;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SRCROOT)/../Products\"",
				);
				HEADER_SEARCH_PATHS = "$(inherited)";
				INFOPLIST_FILE = MineModule/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"WeChatSDK\"",
					"-l\"WeiboSDK\"",
					"-l\"c++\"",
					"-l\"iconv\"",
					"-l\"resolv\"",
					"-l\"resolv.9\"",
					"-l\"sqlite3\"",
					"-l\"xml2\"",
					"-l\"z\"",
					"-framework",
					"\"AFNetworking\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"AVKit\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"AdSupport\"",
					"-framework",
					"\"BUAdSDK\"",
					"-framework",
					"\"BUFoundation\"",
					"-framework",
					"\"BaiduMobAdSDK\"",
					"-framework",
					"\"CMGameSDK\"",
					"-framework",
					"\"CollectionKit\"",
					"-framework",
					"\"CoreData\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreMedia\"",
					"-framework",
					"\"CoreMotion\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"CryptoSwift\"",
					"-framework",
					"\"FDFullscreenPopGesture\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GDTMobSDK\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"KSAdSDK\"",
					"-framework",
					"\"KeychainAccess\"",
					"-framework",
					"\"Kingfisher\"",
					"-framework",
					"\"KingfisherSwiftUI\"",
					"-framework",
					"\"Lottie\"",
					"-framework",
					"\"MJRefresh\"",
					"-framework",
					"\"MapKit\"",
					"-framework",
					"\"MediaPlayer\"",
					"-framework",
					"\"MessageUI\"",
					"-framework",
					"\"MobileCoreServices\"",
					"-framework",
					"\"PhoneNetSDK\"",
					"-framework",
					"\"PromiseKit\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"QuickLook\"",
					"-framework",
					"\"RxBlocking\"",
					"-framework",
					"\"RxCocoa\"",
					"-framework",
					"\"RxRelay\"",
					"-framework",
					"\"RxSwift\"",
					"-framework",
					"\"SafariServices\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SnapKit\"",
					"-framework",
					"\"StoreKit\"",
					"-framework",
					"\"SwiftyJSON\"",
					"-framework",
					"\"SwiftyUserDefaults\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"TencentOpenAPI\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"WebP\"",
					"-framework",
					"\"XMAccount\"",
					"-framework",
					"\"XMBase\"",
					"-framework",
					"\"XMCategories\"",
					"-framework",
					"\"XMKingfisherWebP\"",
					"-framework",
					"\"XMNetworkRequest\"",
					"-framework",
					"\"XMPKHUD\"",
					"-framework",
					"\"XMThirdParty\"",
					"-framework",
					"\"XMUIKit\"",
					"-framework",
					"\"XMWebImage\"",
					"-framework",
					"\"YYCache\"",
					"-framework",
					"\"iCarousel\"",
					"-framework",
					"\"XMReaderModule\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.MineModule;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SUPPORTS_MACCATALYST = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Alpha;
		};
		B0B722DA2351B3D900B23D74 /* Alpha */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6869B130BF881A58B1C88E9A /* Pods-MineModuleTests.alpha.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				INFOPLIST_FILE = MineModuleTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = mh_dylib;
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.MineModuleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Alpha;
		};
		B0B722DB2351B3D900B23D74 /* Alpha */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				COMBINE_HIDPI_IMAGES = YES;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = MineMedia/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Bundles";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.MineMedia;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				VERSIONING_SYSTEM = "";
				WRAPPER_EXTENSION = bundle;
			};
			name = Alpha;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8817FBDE22D873DA00DC0084 /* Build configuration list for PBXProject "MineModule" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8817FBF622D873DA00DC0084 /* Debug */,
				8817FBF722D873DA00DC0084 /* Release */,
				B0B722D82351B3D900B23D74 /* Alpha */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8817FBF822D873DA00DC0084 /* Build configuration list for PBXNativeTarget "MineModule" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8817FBF922D873DA00DC0084 /* Debug */,
				8817FBFA22D873DA00DC0084 /* Release */,
				B0B722D92351B3D900B23D74 /* Alpha */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8817FBFB22D873DA00DC0084 /* Build configuration list for PBXNativeTarget "MineModuleTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8817FBFC22D873DA00DC0084 /* Debug */,
				8817FBFD22D873DA00DC0084 /* Release */,
				B0B722DA2351B3D900B23D74 /* Alpha */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8868CF1322F42EF2009A5F61 /* Build configuration list for PBXNativeTarget "MineMedia" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8868CF1422F42EF2009A5F61 /* Debug */,
				8868CF1522F42EF2009A5F61 /* Release */,
				B0B722DB2351B3D900B23D74 /* Alpha */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8817FBDB22D873DA00DC0084 /* Project object */;
}
