// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		392548694606BA5831DC4628 /* Pods_RouterModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1A1BA14C264D5CB02545AF90 /* Pods_RouterModule.framework */; };
		7925AEDB20956183DA758045 /* Pods_RouterModuleTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 76ACF961F8B256358B70BCFC /* Pods_RouterModuleTests.framework */; };
		85D19EAE2440086F0018B1E7 /* XMRouter+SceneRadio.swift in Sources */ = {isa = PBXBuildFile; fileRef = 85D19EAD2440086E0018B1E7 /* XMRouter+SceneRadio.swift */; };
		88CAD8072318C9E30058EAE5 /* XMRouter+Base.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88CAD8062318C9E30058EAE5 /* XMRouter+Base.swift */; };
		88CAD8092318CBF50058EAE5 /* XMRouter+Login.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88CAD8082318CBF50058EAE5 /* XMRouter+Login.swift */; };
		88CAD80B2318CC0C0058EAE5 /* XMRouter+Audio.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88CAD80A2318CC0C0058EAE5 /* XMRouter+Audio.swift */; };
		88CAD80D2318CC4C0058EAE5 /* XMRouter+Network.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88CAD80C2318CC4C0058EAE5 /* XMRouter+Network.swift */; };
		88CAD80F2318CCB00058EAE5 /* XMRouter+Hybrid.swift in Sources */ = {isa = PBXBuildFile; fileRef = 88CAD80E2318CCB00058EAE5 /* XMRouter+Hybrid.swift */; };
		954344B326CF504A004DA6BD /* XMRouter+Immerse.swift in Sources */ = {isa = PBXBuildFile; fileRef = 954344B226CF504A004DA6BD /* XMRouter+Immerse.swift */; };
		958BDB6F256677F300C4A6D6 /* XMRouter+History.swift in Sources */ = {isa = PBXBuildFile; fileRef = 958BDB6E256677F300C4A6D6 /* XMRouter+History.swift */; };
		959597FA241732B0007280A9 /* XMRouter+VIP.swift in Sources */ = {isa = PBXBuildFile; fileRef = 959597F9241732B0007280A9 /* XMRouter+VIP.swift */; };
		95B44F4224485176000E442F /* XMRouter+Ad.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95B44F4124485176000E442F /* XMRouter+Ad.swift */; };
		95F15582256B9FFD0002372E /* XMRouter+MyListen.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95F15581256B9FFD0002372E /* XMRouter+MyListen.swift */; };
		95FE7C1A27461D1E00613F67 /* XMRouter+Share.swift in Sources */ = {isa = PBXBuildFile; fileRef = 95FE7C1927461D1E00613F67 /* XMRouter+Share.swift */; };
		B06E0CF022FA87D800F4B93D /* RouterModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B06E0CE622FA87D800F4B93D /* RouterModule.framework */; };
		B06E0CF522FA87D800F4B93D /* RouterModuleTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = B06E0CF422FA87D800F4B93D /* RouterModuleTests.swift */; };
		B06E0CF722FA87D800F4B93D /* RouterModule.h in Headers */ = {isa = PBXBuildFile; fileRef = B06E0CE922FA87D800F4B93D /* RouterModule.h */; settings = {ATTRIBUTES = (Public, ); }; };
		B06E0D0122FA87F600F4B93D /* XMRouter.swift in Sources */ = {isa = PBXBuildFile; fileRef = B06E0D0022FA87F600F4B93D /* XMRouter.swift */; };
		B0B014CE2304139400EC7B5C /* XMConfigModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B0B014CD2304139400EC7B5C /* XMConfigModule.framework */; };
		B0DA1371230D6A27008FDE4A /* XMScheme.swift in Sources */ = {isa = PBXBuildFile; fileRef = B0DA1370230D6A27008FDE4A /* XMScheme.swift */; };
		B0EE32BA233726EF00A7364A /* XMUniveralLink.swift in Sources */ = {isa = PBXBuildFile; fileRef = B0EE32B9233726EF00A7364A /* XMUniveralLink.swift */; };
		B0F3058523176B21006C4356 /* XMNetworkModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B0F3058423176B21006C4356 /* XMNetworkModule.framework */; };
		B610D9D62745091A00DA88D5 /* XMRouter+Reader.swift in Sources */ = {isa = PBXBuildFile; fileRef = B610D9D52745091A00DA88D5 /* XMRouter+Reader.swift */; };
		B6454B70279014AF00552757 /* XMRouter+ShortVideo.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6454B6F279014AF00552757 /* XMRouter+ShortVideo.swift */; };
		B645DD08258214DD00065A5E /* XMRouter+tingLite.swift in Sources */ = {isa = PBXBuildFile; fileRef = B645DD07258214DD00065A5E /* XMRouter+tingLite.swift */; };
		B666BBC22726A6F100579168 /* XMRouter+Search.swift in Sources */ = {isa = PBXBuildFile; fileRef = B666BBC12726A6F100579168 /* XMRouter+Search.swift */; };
		B668633827351A00001DC032 /* XMRouter+Book.swift in Sources */ = {isa = PBXBuildFile; fileRef = B668633727351A00001DC032 /* XMRouter+Book.swift */; };
		B677D7232446D0CD0060FEDE /* XMRouter+GobalTask.swift in Sources */ = {isa = PBXBuildFile; fileRef = B677D7222446D0CD0060FEDE /* XMRouter+GobalTask.swift */; };
		B693D369231936EB00E55B30 /* XMRouter+Listen.swift in Sources */ = {isa = PBXBuildFile; fileRef = B693D368231936EB00E55B30 /* XMRouter+Listen.swift */; };
		B693D36B231937E500E55B30 /* XMRouter+Mine.swift in Sources */ = {isa = PBXBuildFile; fileRef = B693D36A231937E500E55B30 /* XMRouter+Mine.swift */; };
		B6BCDE3F24776ABF000C8E57 /* XMRouter+Home.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6BCDE3E24776ABF000C8E57 /* XMRouter+Home.swift */; };
		B6F28CE9278C06E4009C2DC7 /* XMRouter+Comment.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6F28CE8278C06E4009C2DC7 /* XMRouter+Comment.swift */; };
		BB9FAFB92701A0EB00D2668D /* XMRouter+Live.swift in Sources */ = {isa = PBXBuildFile; fileRef = BB9FAFB82701A0EB00D2668D /* XMRouter+Live.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		B06E0CF122FA87D800F4B93D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B06E0CDD22FA87D800F4B93D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B06E0CE522FA87D800F4B93D;
			remoteInfo = RouterModule;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		13357329031EC21691C389C1 /* Pods-RouterModuleTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RouterModuleTests.debug.xcconfig"; path = "Target Support Files/Pods-RouterModuleTests/Pods-RouterModuleTests.debug.xcconfig"; sourceTree = "<group>"; };
		1A1BA14C264D5CB02545AF90 /* Pods_RouterModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_RouterModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2596B51D38745DE27E9E4C59 /* Pods-RouterModuleTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RouterModuleTests.release.xcconfig"; path = "Target Support Files/Pods-RouterModuleTests/Pods-RouterModuleTests.release.xcconfig"; sourceTree = "<group>"; };
		303AF94810B25D460076748D /* Pods-RouterModule.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RouterModule.release.xcconfig"; path = "Target Support Files/Pods-RouterModule/Pods-RouterModule.release.xcconfig"; sourceTree = "<group>"; };
		728202E1ED2980061249DD3F /* Pods-RouterModuleTests.alpha.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RouterModuleTests.alpha.xcconfig"; path = "Target Support Files/Pods-RouterModuleTests/Pods-RouterModuleTests.alpha.xcconfig"; sourceTree = "<group>"; };
		76ACF961F8B256358B70BCFC /* Pods_RouterModuleTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_RouterModuleTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		85D19EAD2440086E0018B1E7 /* XMRouter+SceneRadio.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+SceneRadio.swift"; sourceTree = "<group>"; };
		88CAD8062318C9E30058EAE5 /* XMRouter+Base.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+Base.swift"; sourceTree = "<group>"; };
		88CAD8082318CBF50058EAE5 /* XMRouter+Login.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+Login.swift"; sourceTree = "<group>"; };
		88CAD80A2318CC0C0058EAE5 /* XMRouter+Audio.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+Audio.swift"; sourceTree = "<group>"; };
		88CAD80C2318CC4C0058EAE5 /* XMRouter+Network.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+Network.swift"; sourceTree = "<group>"; };
		88CAD80E2318CCB00058EAE5 /* XMRouter+Hybrid.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+Hybrid.swift"; sourceTree = "<group>"; };
		954344B226CF504A004DA6BD /* XMRouter+Immerse.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+Immerse.swift"; sourceTree = "<group>"; };
		958BDB6E256677F300C4A6D6 /* XMRouter+History.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+History.swift"; sourceTree = "<group>"; };
		959597F9241732B0007280A9 /* XMRouter+VIP.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+VIP.swift"; sourceTree = "<group>"; };
		95B44F4124485176000E442F /* XMRouter+Ad.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+Ad.swift"; sourceTree = "<group>"; };
		95F15581256B9FFD0002372E /* XMRouter+MyListen.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+MyListen.swift"; sourceTree = "<group>"; };
		95FE7C1927461D1E00613F67 /* XMRouter+Share.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+Share.swift"; sourceTree = "<group>"; };
		A11A07D48DD54A053802531B /* Pods-RouterModule.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RouterModule.debug.xcconfig"; path = "Target Support Files/Pods-RouterModule/Pods-RouterModule.debug.xcconfig"; sourceTree = "<group>"; };
		B06E0CE622FA87D800F4B93D /* RouterModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = RouterModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B06E0CE922FA87D800F4B93D /* RouterModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RouterModule.h; sourceTree = "<group>"; };
		B06E0CEA22FA87D800F4B93D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B06E0CEF22FA87D800F4B93D /* RouterModuleTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = RouterModuleTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		B06E0CF422FA87D800F4B93D /* RouterModuleTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RouterModuleTests.swift; sourceTree = "<group>"; };
		B06E0CF622FA87D800F4B93D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B06E0D0022FA87F600F4B93D /* XMRouter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMRouter.swift; sourceTree = "<group>"; };
		B0B014CD2304139400EC7B5C /* XMConfigModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMConfigModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B0DA1370230D6A27008FDE4A /* XMScheme.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMScheme.swift; sourceTree = "<group>"; };
		B0EE32B9233726EF00A7364A /* XMUniveralLink.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XMUniveralLink.swift; sourceTree = "<group>"; };
		B0F3058423176B21006C4356 /* XMNetworkModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = XMNetworkModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B610D9D52745091A00DA88D5 /* XMRouter+Reader.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+Reader.swift"; sourceTree = "<group>"; };
		B6454B6F279014AF00552757 /* XMRouter+ShortVideo.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+ShortVideo.swift"; sourceTree = "<group>"; };
		B645DD07258214DD00065A5E /* XMRouter+tingLite.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+tingLite.swift"; sourceTree = "<group>"; };
		B666BBC12726A6F100579168 /* XMRouter+Search.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+Search.swift"; sourceTree = "<group>"; };
		B668633727351A00001DC032 /* XMRouter+Book.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+Book.swift"; sourceTree = "<group>"; };
		B677D7222446D0CD0060FEDE /* XMRouter+GobalTask.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+GobalTask.swift"; sourceTree = "<group>"; };
		B693D368231936EB00E55B30 /* XMRouter+Listen.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+Listen.swift"; sourceTree = "<group>"; };
		B693D36A231937E500E55B30 /* XMRouter+Mine.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+Mine.swift"; sourceTree = "<group>"; };
		B6BCDE3E24776ABF000C8E57 /* XMRouter+Home.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+Home.swift"; sourceTree = "<group>"; };
		B6F28CE8278C06E4009C2DC7 /* XMRouter+Comment.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+Comment.swift"; sourceTree = "<group>"; };
		BB9FAFB82701A0EB00D2668D /* XMRouter+Live.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "XMRouter+Live.swift"; sourceTree = "<group>"; };
		ED66DF93F7393A86F3827F2A /* Pods-RouterModule.alpha.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RouterModule.alpha.xcconfig"; path = "Target Support Files/Pods-RouterModule/Pods-RouterModule.alpha.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		B06E0CE322FA87D800F4B93D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B0F3058523176B21006C4356 /* XMNetworkModule.framework in Frameworks */,
				B0B014CE2304139400EC7B5C /* XMConfigModule.framework in Frameworks */,
				392548694606BA5831DC4628 /* Pods_RouterModule.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B06E0CEC22FA87D800F4B93D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B06E0CF022FA87D800F4B93D /* RouterModule.framework in Frameworks */,
				7925AEDB20956183DA758045 /* Pods_RouterModuleTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		53F6DA48FFCB002382A9C144 /* Pods */ = {
			isa = PBXGroup;
			children = (
				A11A07D48DD54A053802531B /* Pods-RouterModule.debug.xcconfig */,
				303AF94810B25D460076748D /* Pods-RouterModule.release.xcconfig */,
				13357329031EC21691C389C1 /* Pods-RouterModuleTests.debug.xcconfig */,
				2596B51D38745DE27E9E4C59 /* Pods-RouterModuleTests.release.xcconfig */,
				ED66DF93F7393A86F3827F2A /* Pods-RouterModule.alpha.xcconfig */,
				728202E1ED2980061249DD3F /* Pods-RouterModuleTests.alpha.xcconfig */,
			);
			name = Pods;
			path = ../Pods;
			sourceTree = "<group>";
		};
		B06E0CDC22FA87D800F4B93D = {
			isa = PBXGroup;
			children = (
				B06E0CE822FA87D800F4B93D /* RouterModule */,
				B06E0CF322FA87D800F4B93D /* RouterModuleTests */,
				B06E0CE722FA87D800F4B93D /* Products */,
				53F6DA48FFCB002382A9C144 /* Pods */,
				BB4E6225EE7507C8DEBC61C6 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		B06E0CE722FA87D800F4B93D /* Products */ = {
			isa = PBXGroup;
			children = (
				B06E0CE622FA87D800F4B93D /* RouterModule.framework */,
				B06E0CEF22FA87D800F4B93D /* RouterModuleTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B06E0CE822FA87D800F4B93D /* RouterModule */ = {
			isa = PBXGroup;
			children = (
				B06E0CE922FA87D800F4B93D /* RouterModule.h */,
				B06E0CEA22FA87D800F4B93D /* Info.plist */,
				B06E0D0022FA87F600F4B93D /* XMRouter.swift */,
				88CAD8062318C9E30058EAE5 /* XMRouter+Base.swift */,
				88CAD80A2318CC0C0058EAE5 /* XMRouter+Audio.swift */,
				88CAD80C2318CC4C0058EAE5 /* XMRouter+Network.swift */,
				88CAD8082318CBF50058EAE5 /* XMRouter+Login.swift */,
				88CAD80E2318CCB00058EAE5 /* XMRouter+Hybrid.swift */,
				B693D368231936EB00E55B30 /* XMRouter+Listen.swift */,
				B693D36A231937E500E55B30 /* XMRouter+Mine.swift */,
				959597F9241732B0007280A9 /* XMRouter+VIP.swift */,
				85D19EAD2440086E0018B1E7 /* XMRouter+SceneRadio.swift */,
				B0DA1370230D6A27008FDE4A /* XMScheme.swift */,
				95B44F4124485176000E442F /* XMRouter+Ad.swift */,
				B0EE32B9233726EF00A7364A /* XMUniveralLink.swift */,
				B677D7222446D0CD0060FEDE /* XMRouter+GobalTask.swift */,
				B6BCDE3E24776ABF000C8E57 /* XMRouter+Home.swift */,
				958BDB6E256677F300C4A6D6 /* XMRouter+History.swift */,
				95F15581256B9FFD0002372E /* XMRouter+MyListen.swift */,
				B645DD07258214DD00065A5E /* XMRouter+tingLite.swift */,
				954344B226CF504A004DA6BD /* XMRouter+Immerse.swift */,
				B666BBC12726A6F100579168 /* XMRouter+Search.swift */,
				B610D9D52745091A00DA88D5 /* XMRouter+Reader.swift */,
				B668633727351A00001DC032 /* XMRouter+Book.swift */,
				95FE7C1927461D1E00613F67 /* XMRouter+Share.swift */,
				BB9FAFB82701A0EB00D2668D /* XMRouter+Live.swift */,
				B6F28CE8278C06E4009C2DC7 /* XMRouter+Comment.swift */,
				B6454B6F279014AF00552757 /* XMRouter+ShortVideo.swift */,
			);
			path = RouterModule;
			sourceTree = "<group>";
		};
		B06E0CF322FA87D800F4B93D /* RouterModuleTests */ = {
			isa = PBXGroup;
			children = (
				B06E0CF422FA87D800F4B93D /* RouterModuleTests.swift */,
				B06E0CF622FA87D800F4B93D /* Info.plist */,
			);
			path = RouterModuleTests;
			sourceTree = "<group>";
		};
		BB4E6225EE7507C8DEBC61C6 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				B0F3058423176B21006C4356 /* XMNetworkModule.framework */,
				B0B014CD2304139400EC7B5C /* XMConfigModule.framework */,
				1A1BA14C264D5CB02545AF90 /* Pods_RouterModule.framework */,
				76ACF961F8B256358B70BCFC /* Pods_RouterModuleTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		B06E0CE122FA87D800F4B93D /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B06E0CF722FA87D800F4B93D /* RouterModule.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		B06E0CE522FA87D800F4B93D /* RouterModule */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B06E0CFA22FA87D800F4B93D /* Build configuration list for PBXNativeTarget "RouterModule" */;
			buildPhases = (
				A11694C419FFC682C7B846C8 /* [CP] Check Pods Manifest.lock */,
				B06E0CE122FA87D800F4B93D /* Headers */,
				B06E0CE222FA87D800F4B93D /* Sources */,
				B06E0CE322FA87D800F4B93D /* Frameworks */,
				B06E0CE422FA87D800F4B93D /* Resources */,
				89C4E33C1CD7096F0CF2FB53 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RouterModule;
			productName = RouterModule;
			productReference = B06E0CE622FA87D800F4B93D /* RouterModule.framework */;
			productType = "com.apple.product-type.framework";
		};
		B06E0CEE22FA87D800F4B93D /* RouterModuleTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B06E0CFD22FA87D800F4B93D /* Build configuration list for PBXNativeTarget "RouterModuleTests" */;
			buildPhases = (
				2EC0C92491A0946C0AF67320 /* [CP] Check Pods Manifest.lock */,
				B06E0CEB22FA87D800F4B93D /* Sources */,
				B06E0CEC22FA87D800F4B93D /* Frameworks */,
				B06E0CED22FA87D800F4B93D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B06E0CF222FA87D800F4B93D /* PBXTargetDependency */,
			);
			name = RouterModuleTests;
			productName = RouterModuleTests;
			productReference = B06E0CEF22FA87D800F4B93D /* RouterModuleTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B06E0CDD22FA87D800F4B93D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1100;
				LastUpgradeCheck = 1100;
				ORGANIZATIONNAME = "roy wang";
				TargetAttributes = {
					B06E0CE522FA87D800F4B93D = {
						CreatedOnToolsVersion = 11.0;
						LastSwiftMigration = 1100;
					};
					B06E0CEE22FA87D800F4B93D = {
						CreatedOnToolsVersion = 11.0;
					};
				};
			};
			buildConfigurationList = B06E0CE022FA87D800F4B93D /* Build configuration list for PBXProject "RouterModule" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = B06E0CDC22FA87D800F4B93D;
			productRefGroup = B06E0CE722FA87D800F4B93D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B06E0CE522FA87D800F4B93D /* RouterModule */,
				B06E0CEE22FA87D800F4B93D /* RouterModuleTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B06E0CE422FA87D800F4B93D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B06E0CED22FA87D800F4B93D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		2EC0C92491A0946C0AF67320 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RouterModuleTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		89C4E33C1CD7096F0CF2FB53 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-RouterModule/Pods-RouterModule-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-RouterModule/Pods-RouterModule-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-RouterModule/Pods-RouterModule-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		A11694C419FFC682C7B846C8 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RouterModule-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B06E0CE222FA87D800F4B93D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B693D369231936EB00E55B30 /* XMRouter+Listen.swift in Sources */,
				B645DD08258214DD00065A5E /* XMRouter+tingLite.swift in Sources */,
				959597FA241732B0007280A9 /* XMRouter+VIP.swift in Sources */,
				85D19EAE2440086F0018B1E7 /* XMRouter+SceneRadio.swift in Sources */,
				B677D7232446D0CD0060FEDE /* XMRouter+GobalTask.swift in Sources */,
				B693D36B231937E500E55B30 /* XMRouter+Mine.swift in Sources */,
				958BDB6F256677F300C4A6D6 /* XMRouter+History.swift in Sources */,
				B6454B70279014AF00552757 /* XMRouter+ShortVideo.swift in Sources */,
				B610D9D62745091A00DA88D5 /* XMRouter+Reader.swift in Sources */,
				95FE7C1A27461D1E00613F67 /* XMRouter+Share.swift in Sources */,
				B666BBC22726A6F100579168 /* XMRouter+Search.swift in Sources */,
				B668633827351A00001DC032 /* XMRouter+Book.swift in Sources */,
				BB9FAFB92701A0EB00D2668D /* XMRouter+Live.swift in Sources */,
				88CAD80D2318CC4C0058EAE5 /* XMRouter+Network.swift in Sources */,
				88CAD80B2318CC0C0058EAE5 /* XMRouter+Audio.swift in Sources */,
				B6F28CE9278C06E4009C2DC7 /* XMRouter+Comment.swift in Sources */,
				95F15582256B9FFD0002372E /* XMRouter+MyListen.swift in Sources */,
				B0EE32BA233726EF00A7364A /* XMUniveralLink.swift in Sources */,
				88CAD8072318C9E30058EAE5 /* XMRouter+Base.swift in Sources */,
				88CAD80F2318CCB00058EAE5 /* XMRouter+Hybrid.swift in Sources */,
				95B44F4224485176000E442F /* XMRouter+Ad.swift in Sources */,
				88CAD8092318CBF50058EAE5 /* XMRouter+Login.swift in Sources */,
				B6BCDE3F24776ABF000C8E57 /* XMRouter+Home.swift in Sources */,
				B06E0D0122FA87F600F4B93D /* XMRouter.swift in Sources */,
				B0DA1371230D6A27008FDE4A /* XMScheme.swift in Sources */,
				954344B326CF504A004DA6BD /* XMRouter+Immerse.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B06E0CEB22FA87D800F4B93D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B06E0CF522FA87D800F4B93D /* RouterModuleTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		B06E0CF222FA87D800F4B93D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B06E0CE522FA87D800F4B93D /* RouterModule */;
			targetProxy = B06E0CF122FA87D800F4B93D /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		B06E0CF822FA87D800F4B93D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		B06E0CF922FA87D800F4B93D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "Apple Distribution";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		B06E0CFB22FA87D800F4B93D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A11A07D48DD54A053802531B /* Pods-RouterModule.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMTingModel\"",
					"\"${PODS_ROOT}/XMBase/XMBase/Products\"",
					"\"${PODS_ROOT}/XMCategories\"",
					"\"${PODS_ROOT}/XMDataBase/XMDataBase/Products\"",
					"\"$(SRCROOT)/../Products\"",
				);
				INFOPLIST_FILE = RouterModule/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"sqlite3\"",
					"-framework",
					"\"XMBase\"",
					"-framework",
					"\"XMCategories\"",
					"-framework",
					"\"XMDataBase\"",
					"-framework",
					"\"XMTingModel\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.RouterModule;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		B06E0CFC22FA87D800F4B93D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 303AF94810B25D460076748D /* Pods-RouterModule.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMTingModel\"",
					"\"${PODS_ROOT}/XMBase/XMBase/Products\"",
					"\"${PODS_ROOT}/XMCategories\"",
					"\"${PODS_ROOT}/XMDataBase/XMDataBase/Products\"",
					"\"$(SRCROOT)/../Products\"",
				);
				INFOPLIST_FILE = RouterModule/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"sqlite3\"",
					"-framework",
					"\"XMBase\"",
					"-framework",
					"\"XMCategories\"",
					"-framework",
					"\"XMDataBase\"",
					"-framework",
					"\"XMTingModel\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.RouterModule;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		B06E0CFE22FA87D800F4B93D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 13357329031EC21691C389C1 /* Pods-RouterModuleTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = AS4ANJJUVM;
				INFOPLIST_FILE = RouterModuleTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.RouterModuleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		B06E0CFF22FA87D800F4B93D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2596B51D38745DE27E9E4C59 /* Pods-RouterModuleTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = AS4ANJJUVM;
				INFOPLIST_FILE = RouterModuleTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.RouterModuleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		B0B723042351B64500B23D74 /* Alpha */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "Apple Distribution";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"ALPHA=1",
					"DEBUG=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "ALPHA DEBUG";
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Alpha;
		};
		B0B723052351B64500B23D74 /* Alpha */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ED66DF93F7393A86F3827F2A /* Pods-RouterModule.alpha.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/XMTingModel\"",
					"\"${PODS_ROOT}/XMBase/XMBase/Products\"",
					"\"${PODS_ROOT}/XMCategories\"",
					"\"${PODS_ROOT}/XMDataBase/XMDataBase/Products\"",
					"\"$(SRCROOT)/../Products\"",
				);
				INFOPLIST_FILE = RouterModule/Info.plist;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"sqlite3\"",
					"-framework",
					"\"XMBase\"",
					"-framework",
					"\"XMCategories\"",
					"-framework",
					"\"XMDataBase\"",
					"-framework",
					"\"XMTingModel\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.RouterModule;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Alpha;
		};
		B0B723062351B64500B23D74 /* Alpha */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 728202E1ED2980061249DD3F /* Pods-RouterModuleTests.alpha.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = AS4ANJJUVM;
				INFOPLIST_FILE = RouterModuleTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.ximalaya.RouterModuleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Alpha;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B06E0CE022FA87D800F4B93D /* Build configuration list for PBXProject "RouterModule" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B06E0CF822FA87D800F4B93D /* Debug */,
				B06E0CF922FA87D800F4B93D /* Release */,
				B0B723042351B64500B23D74 /* Alpha */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B06E0CFA22FA87D800F4B93D /* Build configuration list for PBXNativeTarget "RouterModule" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B06E0CFB22FA87D800F4B93D /* Debug */,
				B06E0CFC22FA87D800F4B93D /* Release */,
				B0B723052351B64500B23D74 /* Alpha */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B06E0CFD22FA87D800F4B93D /* Build configuration list for PBXNativeTarget "RouterModuleTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B06E0CFE22FA87D800F4B93D /* Debug */,
				B06E0CFF22FA87D800F4B93D /* Release */,
				B0B723062351B64500B23D74 /* Alpha */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = B06E0CDD22FA87D800F4B93D /* Project object */;
}
